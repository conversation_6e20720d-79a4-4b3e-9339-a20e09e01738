{"singleQuote": true, "editor.detectIndentation": false, "editor.tabSize": 2, "workbench.colorTheme": "One Dark Pro", "workbench.iconTheme": "vscode-icons", "vsicons.dontShowNewVersionMessage": true, "editor.fontSize": 18, "workbench.startupEditor": "none", "trailingComma": "none", "semi": true, "printWidth": 120, "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": [], "icon": "terminal-cmd"}, "GitBash": {"path": ["D:\\Program Files\\Git\\bin\\bash.exe"], "args": ["-l"], "icon": "terminal-bash"}}, "terminal.integrated.defaultProfile.windows": "GitBash", "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "bracketPairColorizer.depreciation-notice": false, "code-runner.runInTerminal": true, "window.zoomLevel": 0.8, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "workbench.editor.splitInGroupLayout": "vertical", "git.confirmSync": false, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.language.brackets": false, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "auto-close-tag.activationOnLanguage": ["xml", "php", "blade", "ejs", "jinja", "javascript", "javascriptreact", "typescript", "typescriptreact", "plaintext", "markdown", "vue", "liquid", "erb", "lang-cfml", "cfml", "HTML (EEx)", "HTML (Eex)", "plist"], "editor.maxTokenizationLineLength": 200000, "auto-close-tag.disableOnLanguage": [], "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "explorer.confirmDelete": false, "[vue]": {"editor.defaultFormatter": "octref.vetur"}, "eslint.run": "onType", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}