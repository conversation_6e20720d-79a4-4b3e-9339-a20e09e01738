import request from '@/utils/request';

// 获取环境数据
export function getEnvironmentData() {
  return request({
    url: '/big_screen_second/getEnvironmentData',
    method: 'get'
  });
}
//智慧预测
export function getWisdomPrediction() {
  {
    return request({
      url: '/big_screen_second/getWisdomPrediction',
      method: 'get'
    });
  }
}

// 获取气象数据走势
export function getWeatherTrend() {
  return request({
    url: '/big_screen_second/getWeatherDataTrend',
    method: 'get'
  });
}

// 获取生态环境分析数据
export function getEnvironmentAnalysis() {
  return request({
    url: '/big_screen_second/getEcologicalEnvironmentAnalysis',
    method: 'get'
  });
}

// 获取设备在线率统计
export function getDeviceOnlineStats() {
  return request({
    url: '/big_screen_second/getDeviceOnlineRate',
    method: 'get'
  });
}

// 获取设备离线告警列表
export function getDeviceOfflineAlarms() {
  return request({
    url: '/big_screen_second/getDeviceOfflineWarning',
    method: 'get'
  });
}

// 获取异常预警
export function getAbnormalWarnings() {
  return request({
    url: '/big_screen_second/getAbnormalWarning',
    method: 'get'
  });
}

// 获取设备在线统计
export function getHomeStats() {
  return request({
    url: '/big_screen_second/getDeviceNumberStatistics',
    method: 'get'
  });
}

//获取地图
export function getMapData() {
  return request({
    url: '/big_screen_second/getMapData',
    method: 'get'
  });
}

// 获取设备列表
export function getDeviceListByBaseId() {
  return request({
    url: `/big_screen_second/getDeviceListByBaseId`,
    method: 'get'
  });
}
//电子围栏列表
export function getFenceList() {
  return request({
    url: `/big_screen_second/getFenceList`,
    method: 'get'
  });
}
