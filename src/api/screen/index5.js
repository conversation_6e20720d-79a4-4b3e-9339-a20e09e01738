import request from '@/utils/request';

// ��ȡ�ӹ����ͳ������
export function getProcessingComparison() {
  return request({
    url: '/big_screen_fifth/getProcessType',
    method: 'get'
  });
}

// ��ȡ�ӹ��˲�ȱ�ݷ�������
export function getDefectAnalysis() {
  return request({
    url: '/big_screen_fifth/getDefectAnalysis',
    method: 'get'
  });
}

// ��ȡ�ӹ�����ͳ������
// export function getProductionStats() {
//   return request({
//     url: '/big_screen_fifth/getProductionStatistics',
//     method: 'get'
//   });
// }
export function getProductionStats(type='人参酒') {
  return request({
    url: '/big_screen_fifth/getProductionStatistics',
    method: 'get',
    params: {
      type: type 
    }
  });
}

// ��ȡ����ͳ������
export function getTopStatistics() {
  return request({
    url: '/big_screen_fifth/getCenterData',
    method: 'get'
  });
}

// ��ȡ�ײ�ͳ������
export function getBottomStatistics() {
  return request({
    url: '/big_screen_fifth/bottom-stats',
    method: 'get'
  });
}

// ��ȡ�ӹ���������ͳ��
export function getProductionProgress(type='人参酒') {
  return request({
    url: '/big_screen_fifth/getProductionProgress',
    method: 'get',
    params: {
      type: type 
    }
  });
}

// ��ȡ�ӹ������ܺ�ͳ��
export function getEnergyConsumption() {
  return request({
    url: '/big_screen_fifth/getEnergyConsumption',
    method: 'get'
  });
}

// ��ȡ�ӹ���Ʒ���ͳ��
export function getInventoryStats() {
  return request({
    url: '/big_screen_fifth/getInventoryStatistics',
    method: 'get'
  });
}
