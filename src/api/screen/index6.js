import request from '@/utils/request';

// 获取人参价格趋势数据
export function getGinsengPriceTrend(params) {
  return request({
    url: '/big_screen_sixth/price_trend',
    method: 'get',
    params
  });
}

// 获取人参销量排名数据
export function getGinsengSalesRanking() {
  return request({
    url: '/big_screen_sixth/sales_ranking',
    method: 'get'
  });
}

// 获取年度统计数据
export function getAnnualStatistics() {
  return request({
    url: '/big_screen_sixth/getTopData',
    method: 'get'
  });
}

// 获取人参产品交易额数据
export function getGinsengTransactionAmount() {
  return request({
    url: '/big_screen_sixth/transaction_amount',
    method: 'get'
  });
}

// 获取交易额增长趋势图
export function getTransactionGrowthTrend() {
  return request({
    url: '/big_screen_sixth/transaction_trend',
    method: 'get'
  });
}

// 获取产业主体销售额排名
export function getIndustrySalesRanking() {
  return request({
    url: '/big_screen_sixth/sales_ranking2',
    method: 'get'
  });
}

// 获取销售渠道占比数据
export function getSalesChannelRatio() {
  return request({
    url: '/big_screen_sixth/sales_channel',
    method: 'get'
  });
}

// 获取出口规模及增速数据
export function getExportScaleAndGrowth() {
  return request({
    url: '/big_screen_sixth/export_scale',
    method: 'get'
  });
}

// 获取实时物流数据
export function getRealTimeLogistics() {
  return request({
    url: '/big_screen_sixth/logistics_data',
    method: 'get'
  });
}

// 获取文旅客流和营收监测数据
export function getTourismFlowAndRevenue() {
  return request({
    url: '/big_screen_sixth/customer_revenue',
    method: 'get'
  });
}

// 获取产品列表数据
export function getProductList() {
  return request({
    url: '/big_screen_sixth/product_list',
    method: 'get'
  });
}


//获取加工类比统计
export function getProcessType() {
  return request({
    url: '/big_screen_fifth/getProcessType',
    method: 'get'
  });
}
