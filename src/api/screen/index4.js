import request from '@/utils/request';

// 获取林下参种植分布数据
export function getPlantingDistribution() {
  return request({
    url: '/big_screen_fourth/plantingDistribution',
    method: 'get'
  });
}

// 获取林下参种主体种植排名
export function getPlantingRanking() {
  return request({
    url: '/big_screen_fourth/plantingRanking',
    method: 'get'
  });
}

// 获取种源种植面积比例
export function getPlantingAreaRatio() {
  return request({
    url: '/big_screen_fourth/sourcePlantingArea',
    method: 'get'
  });
}

// 获取顶部统计数据
export function getTopStatistics() {
  return request({
    url: '/big_screen_fourth/dataStatistics',
    method: 'get'
  });
}

// 获取年度人参产量数据
export function getAnnualOutput(params) {
  return request({
    url: '/big_screen_fourth/annualProduction',
    method: 'get',
    params
  });
}

// 获取人参成活率统计
export function getSurvivalRate() {
  return request({
    url: '/big_screen_fourth/survivalRate',
    method: 'get'
  });
}

// 获取本月任务数据
export function getMonthlyTasks(params) {
  return request({
    url: '/big_screen_fourth/monthTaskStatistics',
    method: 'get',
    params
  });
}

// 获取历史任务完成率统计
export function getHistoricalTaskCompletion() {
  return request({
    url: '/big_screen_fourth/taskCompletionRate',
    method: 'get'
  });
}

// 获取任务列表
export function getTaskList(params) {
  return request({
    url: '/big_screen_fourth/taskList',
    method: 'get',
    params
  });
}
//获取地图
export function getMapData() {
  return request({
    url: '/big_screen_fourth/mapData',
    method: 'get'
  });
}
