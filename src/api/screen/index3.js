import request from '@/utils/request';

// 获取服务企业类型统计
export function getEnterpriseTypes() {
  return request({
    url: '/big_screen_third/getEnterpriseType',
    method: 'get'
  });
}

// 获取服务企业词云数据
export function getEnterpriseWordCloud() {
  return request({
    url: '/big_screen_third/getEnterpriseWordCloud',
    method: 'get'
  });
}

// 获取企业信用评级
export function getCreditRating() {
  return request({
    url: '/big_screen_third/getEnterpriseCreditRating',
    method: 'get'
  });
}

// 获取金融统计数据（授信额度、授信笔数、服务企业）
export function getFinanceStats() {
  return request({
    url: '/big_screen_third/getFinanceStats',
    method: 'get'
  });
}

// 获取企业主体分布（种植主体、加工企业、销售企业、金融机构）
export function getEnterpriseDistribution() {
  return request({
    url: '/big_screen_third/getEnterpriseDistribution',
    method: 'get'
  });
}

// 获取融资授信趋势数据
export function getFinancingTrend() {
  return request({
    url: '/big_screen_third/getFinancingCreditTrend',
    method: 'get'
  });
}

// 获取产品授信分布数据
export function getProductCreditDistribution() {
  return request({
    url: '/big_screen_third/getProductCreditDistribution',
    method: 'get'
  });
}

// 获取融资产品类型分布
export function getFinanceProductTypes() {
  return request({
    url: '/big_screen_third/getFinanceProductTypes',
    method: 'get'
  });
}

// 获取风险预警提示列表
export function getRiskWarnings() {
  return request({
    url: '/big_screen_third/getRiskWarnings',
    method: 'get'
  });
}

// 获取银行放款排名
export function getBankLoanRanking() {
  return request({
    url: '/big_screen_third/getBankRelaxRanking',
    method: 'get'
  });
}

// 获取所有统计数据（一次性获取所有数据）
export function getAllStats() {
  return request({
    url: '/big_screen_third/getCenterData',
    method: 'get'
  });
}


//贷后预警提示
export function getRiskWarningTips() {
  return request({
    url: '/big_screen_third/getRiskWarningTips',
    method: 'get'
  });
}
