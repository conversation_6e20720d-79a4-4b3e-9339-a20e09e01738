import request from '@/utils/request';

// 获取主体数量统计
export function getEntityCount() {
  return request({
    url: '/big_screen_first/subject_count',
    method: 'get'
  });
}

// 获取宜参面积统计
export function getAreaStats() {
  return request({
    url: '/big_screen_first/area_count',
    method: 'get'
  });
}

// 获取人参存量统计
export function getGinsengInventory() {
  return request({
    url: '/big_screen_first/stock_count',
    method: 'get'
  });
}

// 获取种植标准任务
export function getPlantingTasks() {
  return request({
    url: '/big_screen_first/task_count',
    method: 'get'
  });
}

// 获取科技赋能情况
export function getTechEmpowerment() {
  return request({
    url: '/big_screen_first/ability_count',
    method: 'get'
  });
}

// 获取交易数据走势
export function getTransactionTrend() {
  return request({
    url: '/big_screen_first/trade_trend',
    method: 'get'
  });
}

// 获取参产品加工情况
export function getProcessingStatus() {
  return request({
    url: '/big_screen_first/product_process',
    method: 'get'
  });
}

// 获取金融助力情况
export function getFinancialSupport() {
  return request({
    url: '/big_screen_first/financial_help',
    method: 'get'
  });
}

// 获取首页统计数据
export function getHomeStats() {
  return request({
    url: '/big_screen_first/center_data',
    method: 'get'
  });
}
