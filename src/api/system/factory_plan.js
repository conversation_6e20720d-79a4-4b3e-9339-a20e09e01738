import request from '@/utils/request'

// 查询加工计划管理列表
export function listFactory_plan(query) {
  return request({
    url: '/system/factory_plan/list',
    method: 'get',
    params: query
  })
}

// 查询加工计划管理详细
export function getFactory_plan(id) {
  return request({
    url: '/system/factory_plan/' + id,
    method: 'get'
  })
}

// 新增加工计划管理
export function addFactory_plan(data) {
  return request({
    url: '/system/factory_plan',
    method: 'post',
    data: data
  })
}

// 修改加工计划管理
export function updateFactory_plan(data) {
  return request({
    url: '/system/factory_plan',
    method: 'put',
    data: data
  })
}

// 删除加工计划管理
export function delFactory_plan(id) {
  return request({
    url: '/system/factory_plan/' + id,
    method: 'delete'
  })
}
