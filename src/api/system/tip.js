import request from '@/utils/request'

// 查询风险预警提示列表
export function listTip(query) {
  return request({
    url: '/system/tip/list',
    method: 'get',
    params: query
  })
}

// 查询风险预警提示详细
export function getTip(id) {
  return request({
    url: '/system/tip/' + id,
    method: 'get'
  })
}

// 新增风险预警提示
export function addTip(data) {
  return request({
    url: '/system/tip',
    method: 'post',
    data: data
  })
}

// 修改风险预警提示
export function updateTip(data) {
  return request({
    url: '/system/tip',
    method: 'put',
    data: data
  })
}

// 删除风险预警提示
export function delTip(id) {
  return request({
    url: '/system/tip/' + id,
    method: 'delete'
  })
}
