import request from '@/utils/request'

// 查询加工人参缺陷管理列表
export function listQx(query) {
  return request({
    url: '/system/qx/list',
    method: 'get',
    params: query
  })
}

// 查询加工人参缺陷管理详细
export function getQx(id) {
  return request({
    url: '/system/qx/' + id,
    method: 'get'
  })
}

// 新增加工人参缺陷管理
export function addQx(data) {
  return request({
    url: '/system/qx',
    method: 'post',
    data: data
  })
}

// 修改加工人参缺陷管理
export function updateQx(data) {
  return request({
    url: '/system/qx',
    method: 'put',
    data: data
  })
}

// 删除加工人参缺陷管理
export function delQx(id) {
  return request({
    url: '/system/qx/' + id,
    method: 'delete'
  })
}
