import request from '@/utils/request'

// 查询人参成活率统计列表
export function listRs_ch(query) {
  return request({
    url: '/system/rs_ch/list',
    method: 'get',
    params: query
  })
}

// 查询人参成活率统计详细
export function getRs_ch(id) {
  return request({
    url: '/system/rs_ch/' + id,
    method: 'get'
  })
}

// 新增人参成活率统计
export function addRs_ch(data) {
  return request({
    url: '/system/rs_ch',
    method: 'post',
    data: data
  })
}

// 修改人参成活率统计
export function updateRs_ch(data) {
  return request({
    url: '/system/rs_ch',
    method: 'put',
    data: data
  })
}

// 删除人参成活率统计
export function delRs_ch(id) {
  return request({
    url: '/system/rs_ch/' + id,
    method: 'delete'
  })
}
