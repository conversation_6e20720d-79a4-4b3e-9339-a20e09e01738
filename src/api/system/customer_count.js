import request from '@/utils/request'

// 查询客流管理列表
export function listCustomer_count(query) {
  return request({
    url: '/system/customer_count/list',
    method: 'get',
    params: query
  })
}

// 查询客流管理详细
export function getCustomer_count(id) {
  return request({
    url: '/system/customer_count/' + id,
    method: 'get'
  })
}

// 新增客流管理
export function addCustomer_count(data) {
  return request({
    url: '/system/customer_count',
    method: 'post',
    data: data
  })
}

// 修改客流管理
export function updateCustomer_count(data) {
  return request({
    url: '/system/customer_count',
    method: 'put',
    data: data
  })
}

// 删除客流管理
export function delCustomer_count(id) {
  return request({
    url: '/system/customer_count/' + id,
    method: 'delete'
  })
}
