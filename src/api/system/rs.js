import request from '@/utils/request'

// 查询人参存量统计管理列表
export function listRs(query) {
  return request({
    url: '/system/rs/list',
    method: 'get',
    params: query
  })
}

// 查询人参存量统计管理详细
export function getRs(id) {
  return request({
    url: '/system/rs/' + id,
    method: 'get'
  })
}

// 新增人参存量统计管理
export function addRs(data) {
  return request({
    url: '/system/rs',
    method: 'post',
    data: data
  })
}

// 修改人参存量统计管理
export function updateRs(data) {
  return request({
    url: '/system/rs',
    method: 'put',
    data: data
  })
}

// 删除人参存量统计管理
export function delRs(id) {
  return request({
    url: '/system/rs/' + id,
    method: 'delete'
  })
}
