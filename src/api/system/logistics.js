import request from '@/utils/request'

// 查询物流管理列表
export function listLogistics(query) {
  return request({
    url: '/system/logistics/list',
    method: 'get',
    params: query
  })
}

// 查询物流管理详细
export function getLogistics(id) {
  return request({
    url: '/system/logistics/' + id,
    method: 'get'
  })
}

// 新增物流管理
export function addLogistics(data) {
  return request({
    url: '/system/logistics',
    method: 'post',
    data: data
  })
}

// 修改物流管理
export function updateLogistics(data) {
  return request({
    url: '/system/logistics',
    method: 'put',
    data: data
  })
}

// 删除物流管理
export function delLogistics(id) {
  return request({
    url: '/system/logistics/' + id,
    method: 'delete'
  })
}
