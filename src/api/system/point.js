import request from '@/utils/request'

// 查询加工厂管理列表
export function listPoint(query) {
    return request({
        url: '/system/unity-point-manage/list',
        method: 'get',
        params: query
    })
}

// 查询加工厂管理详细
export function getPoint(id) {
    return request({
        url: '/system/unity-point-manage/' + id,
        method: 'get'
    })
}

// 新增点位管理
export function addPoint(data) {
    return request({
        url: '/system/unity-point-manage/add',
        method: 'post',
        data: data
    })
}

// 修改加工厂管理
export function updatePoint(data) {
    return request({
        url: '/system/unity-point-manage',
        method: 'put',
        data: data
    })
}

// 删除加工厂管理
export function delPoint(id) {
    return request({
        url: '/system/unity-point-manage/' + id,
        method: 'delete'
    })
}