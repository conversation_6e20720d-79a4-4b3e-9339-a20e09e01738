import request from '@/utils/request'

// 查询融资产品类型列表
export function listFinancial_product(query) {
  return request({
    url: '/system/financial_product/list',
    method: 'get',
    params: query
  })
}

// 查询融资产品类型详细
export function getFinancial_product(id) {
  return request({
    url: '/system/financial_product/' + id,
    method: 'get'
  })
}

// 新增融资产品类型
export function addFinancial_product(data) {
  return request({
    url: '/system/financial_product',
    method: 'post',
    data: data
  })
}

// 修改融资产品类型
export function updateFinancial_product(data) {
  return request({
    url: '/system/financial_product',
    method: 'put',
    data: data
  })
}

// 删除融资产品类型
export function delFinancial_product(id) {
  return request({
    url: '/system/financial_product/' + id,
    method: 'delete'
  })
}
