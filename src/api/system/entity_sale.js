import request from '@/utils/request'

// 查询产业主体销售额管理列表
export function listEntity_sale(query) {
  return request({
    url: '/system/entity_sale/list',
    method: 'get',
    params: query
  })
}

// 查询产业主体销售额管理详细
export function getEntity_sale(id) {
  return request({
    url: '/system/entity_sale/' + id,
    method: 'get'
  })
}

// 新增产业主体销售额管理
export function addEntity_sale(data) {
  return request({
    url: '/system/entity_sale',
    method: 'post',
    data: data
  })
}

// 修改产业主体销售额管理
export function updateEntity_sale(data) {
  return request({
    url: '/system/entity_sale',
    method: 'put',
    data: data
  })
}

// 删除产业主体销售额管理
export function delEntity_sale(id) {
  return request({
    url: '/system/entity_sale/' + id,
    method: 'delete'
  })
}
