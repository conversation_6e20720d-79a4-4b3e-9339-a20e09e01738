import request from '@/utils/request'

// 查询种植标准任务管理列表
export function listJob(query) {
  return request({
    url: '/system/job/list',
    method: 'get',
    params: query
  })
}

// 查询种植标准任务管理详细
export function getJob(id) {
  return request({
    url: '/system/job/' + id,
    method: 'get'
  })
}

// 新增种植标准任务管理
export function addJob(data) {
  return request({
    url: '/system/job',
    method: 'post',
    data: data
  })
}

// 修改种植标准任务管理
export function updateJob(data) {
  return request({
    url: '/system/job',
    method: 'put',
    data: data
  })
}

// 删除种植标准任务管理
export function delJob(id) {
  return request({
    url: '/system/job/' + id,
    method: 'delete'
  })
}
