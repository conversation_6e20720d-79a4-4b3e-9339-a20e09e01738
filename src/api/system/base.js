import request from '@/utils/request'

// 查询基地列表
export function listBase(query) {
  return request({
    url: '/system/base/list',
    method: 'get',
    params: query
  })
}

// 查询基地详细
export function getBase(id) {
  return request({
    url: '/system/base/' + id,
    method: 'get'
  })
}

// 新增基地
export function addBase(data) {
  return request({
    url: '/system/base',
    method: 'post',
    data: data
  })
}

// 修改基地
export function updateBase(data) {
  return request({
    url: '/system/base',
    method: 'put',
    data: data
  })
}

// 删除基地
export function delBase(id) {
  return request({
    url: '/system/base/' + id,
    method: 'delete'
  })
}
