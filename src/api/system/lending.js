import request from '@/utils/request'

// 查询贷款管理列表
export function listLending(query) {
  return request({
    url: '/system/lending/list',
    method: 'get',
    params: query
  })
}

// 查询贷款管理详细
export function getLending(id) {
  return request({
    url: '/system/lending/' + id,
    method: 'get'
  })
}

// 新增贷款管理
export function addLending(data) {
  return request({
    url: '/system/lending',
    method: 'post',
    data: data
  })
}

// 修改贷款管理
export function updateLending(data) {
  return request({
    url: '/system/lending',
    method: 'put',
    data: data
  })
}

// 删除贷款管理
export function delLending(id) {
  return request({
    url: '/system/lending/' + id,
    method: 'delete'
  })
}
