import request from '@/utils/request'

// 查询销售渠道管理列表
export function listChannel(query) {
  return request({
    url: '/system/channel/list',
    method: 'get',
    params: query
  })
}

// 查询销售渠道管理详细
export function getChannel(id) {
  return request({
    url: '/system/channel/' + id,
    method: 'get'
  })
}

// 新增销售渠道管理
export function addChannel(data) {
  return request({
    url: '/system/channel',
    method: 'post',
    data: data
  })
}

// 修改销售渠道管理
export function updateChannel(data) {
  return request({
    url: '/system/channel',
    method: 'put',
    data: data
  })
}

// 删除销售渠道管理
export function delChannel(id) {
  return request({
    url: '/system/channel/' + id,
    method: 'delete'
  })
}
