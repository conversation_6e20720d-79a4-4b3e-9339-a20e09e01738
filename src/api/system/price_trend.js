import request from '@/utils/request'

// 查询人参价格趋势管理列表
export function listPrice_trend(query) {
  return request({
    url: '/system/price_trend/list',
    method: 'get',
    params: query
  })
}

// 查询人参价格趋势管理详细
export function getPrice_trend(id) {
  return request({
    url: '/system/price_trend/' + id,
    method: 'get'
  })
}

// 新增人参价格趋势管理
export function addPrice_trend(data) {
  return request({
    url: '/system/price_trend',
    method: 'post',
    data: data
  })
}

// 修改人参价格趋势管理
export function updatePrice_trend(data) {
  return request({
    url: '/system/price_trend',
    method: 'put',
    data: data
  })
}

// 删除人参价格趋势管理
export function delPrice_trend(id) {
  return request({
    url: '/system/price_trend/' + id,
    method: 'delete'
  })
}
