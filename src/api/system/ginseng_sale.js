import request from '@/utils/request'

// 查询人参销量排名管理列表
export function listGinseng_sale(query) {
  return request({
    url: '/system/ginseng_sale/list',
    method: 'get',
    params: query
  })
}

// 查询人参销量排名管理详细
export function getGinseng_sale(id) {
  return request({
    url: '/system/ginseng_sale/' + id,
    method: 'get'
  })
}

// 新增人参销量排名管理
export function addGinseng_sale(data) {
  return request({
    url: '/system/ginseng_sale',
    method: 'post',
    data: data
  })
}

// 修改人参销量排名管理
export function updateGinseng_sale(data) {
  return request({
    url: '/system/ginseng_sale',
    method: 'put',
    data: data
  })
}

// 删除人参销量排名管理
export function delGinseng_sale(id) {
  return request({
    url: '/system/ginseng_sale/' + id,
    method: 'delete'
  })
}
