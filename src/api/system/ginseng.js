import request from '@/utils/request'

// 查询种植管理列表
export function listGinseng(query) {
  return request({
    url: '/system/ginseng/list',
    method: 'get',
    params: query
  })
}

// 查询种植管理详细
export function getGinseng(id) {
  return request({
    url: '/system/ginseng/' + id,
    method: 'get'
  })
}

// 新增种植管理
export function addGinseng(data) {
  return request({
    url: '/system/ginseng',
    method: 'post',
    data: data
  })
}

// 修改种植管理
export function updateGinseng(data) {
  return request({
    url: '/system/ginseng',
    method: 'put',
    data: data
  })
}

// 删除种植管理
export function delGinseng(id) {
  return request({
    url: '/system/ginseng/' + id,
    method: 'delete'
  })
}
