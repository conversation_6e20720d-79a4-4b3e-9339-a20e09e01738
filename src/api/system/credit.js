import request from '@/utils/request'

// 查询授信列表
export function listCredit(query) {
  return request({
    url: '/system/credit/list',
    method: 'get',
    params: query
  })
}

// 查询授信详细
export function getCredit(id) {
  return request({
    url: '/system/credit/' + id,
    method: 'get'
  })
}

// 新增授信
export function addCredit(data) {
  return request({
    url: '/system/credit',
    method: 'post',
    data: data
  })
}

// 修改授信
export function updateCredit(data) {
  return request({
    url: '/system/credit',
    method: 'put',
    data: data
  })
}

// 删除授信
export function delCredit(id) {
  return request({
    url: '/system/credit/' + id,
    method: 'delete'
  })
}
