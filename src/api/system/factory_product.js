import request from '@/utils/request'

// 查询人参产品加工记录管理列表
export function listFactory_product(query) {
  return request({
    url: '/system/factory_product/list',
    method: 'get',
    params: query
  })
}

// 查询人参产品加工记录管理详细
export function getFactory_product(id) {
  return request({
    url: '/system/factory_product/' + id,
    method: 'get'
  })
}

// 新增人参产品加工记录管理
export function addFactory_product(data) {
  return request({
    url: '/system/factory_product',
    method: 'post',
    data: data
  })
}

// 修改人参产品加工记录管理
export function updateFactory_product(data) {
  return request({
    url: '/system/factory_product',
    method: 'put',
    data: data
  })
}

// 删除人参产品加工记录管理
export function delFactory_product(id) {
  return request({
    url: '/system/factory_product/' + id,
    method: 'delete'
  })
}
