import request from '@/utils/request'

// 查询气象管理列表
export function listWeather(query) {
  return request({
    url: '/system/weather/list',
    method: 'get',
    params: query
  })
}

// 查询气象管理详细
export function getWeather(id) {
  return request({
    url: '/system/weather/' + id,
    method: 'get'
  })
}

// 新增气象管理
export function addWeather(data) {
  return request({
    url: '/system/weather',
    method: 'post',
    data: data
  })
}

// 修改气象管理
export function updateWeather(data) {
  return request({
    url: '/system/weather',
    method: 'put',
    data: data
  })
}

// 删除气象管理
export function delWeather(id) {
  return request({
    url: '/system/weather/' + id,
    method: 'delete'
  })
}
