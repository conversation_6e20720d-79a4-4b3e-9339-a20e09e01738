import request from '@/utils/request'

// 查询人参产品信息管理列表
export function listGinseng_product(query) {
  return request({
    url: '/system/ginseng_product/list',
    method: 'get',
    params: query
  })
}

// 查询人参产品信息管理详细
export function getGinseng_product(id) {
  return request({
    url: '/system/ginseng_product/' + id,
    method: 'get'
  })
}

// 新增人参产品信息管理
export function addGinseng_product(data) {
  return request({
    url: '/system/ginseng_product',
    method: 'post',
    data: data
  })
}

// 修改人参产品信息管理
export function updateGinseng_product(data) {
  return request({
    url: '/system/ginseng_product',
    method: 'put',
    data: data
  })
}

// 删除人参产品信息管理
export function delGinseng_product(id) {
  return request({
    url: '/system/ginseng_product/' + id,
    method: 'delete'
  })
}
