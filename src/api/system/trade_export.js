import request from '@/utils/request'

// 查询交易/出口数据管理列表
export function listTrade_export(query) {
  return request({
    url: '/system/trade_export/list',
    method: 'get',
    params: query
  })
}

// 查询交易/出口数据管理详细
export function getTrade_export(id) {
  return request({
    url: '/system/trade_export/' + id,
    method: 'get'
  })
}

// 新增交易/出口数据管理
export function addTrade_export(data) {
  return request({
    url: '/system/trade_export',
    method: 'post',
    data: data
  })
}

// 修改交易/出口数据管理
export function updateTrade_export(data) {
  return request({
    url: '/system/trade_export',
    method: 'put',
    data: data
  })
}

// 删除交易/出口数据管理
export function delTrade_export(id) {
  return request({
    url: '/system/trade_export/' + id,
    method: 'delete'
  })
}
