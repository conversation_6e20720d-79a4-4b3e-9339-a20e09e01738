import request from '@/utils/request'

// 查询种源种植面积统计管理列表
export function listProvenance_name(query) {
  return request({
    url: '/system/provenance_name/list',
    method: 'get',
    params: query
  })
}

// 查询种源种植面积统计管理详细
export function getProvenance_name(id) {
  return request({
    url: '/system/provenance_name/' + id,
    method: 'get'
  })
}

// 新增种源种植面积统计管理
export function addProvenance_name(data) {
  return request({
    url: '/system/provenance_name',
    method: 'post',
    data: data
  })
}

// 修改种源种植面积统计管理
export function updateProvenance_name(data) {
  return request({
    url: '/system/provenance_name',
    method: 'put',
    data: data
  })
}

// 删除种源种植面积统计管理
export function delProvenance_name(id) {
  return request({
    url: '/system/provenance_name/' + id,
    method: 'delete'
  })
}
