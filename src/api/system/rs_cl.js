import request from '@/utils/request'

// 查询人参产量统计列表
export function listRs_cl(query) {
  return request({
    url: '/system/rs_cl/list',
    method: 'get',
    params: query
  })
}

// 查询人参产量统计详细
export function getRs_cl(id) {
  return request({
    url: '/system/rs_cl/' + id,
    method: 'get'
  })
}

// 新增人参产量统计
export function addRs_cl(data) {
  return request({
    url: '/system/rs_cl',
    method: 'post',
    data: data
  })
}

// 修改人参产量统计
export function updateRs_cl(data) {
  return request({
    url: '/system/rs_cl',
    method: 'put',
    data: data
  })
}

// 删除人参产量统计
export function delRs_cl(id) {
  return request({
    url: '/system/rs_cl/' + id,
    method: 'delete'
  })
}
