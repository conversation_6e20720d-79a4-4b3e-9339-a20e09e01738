import request from '@/utils/request'

// 查询交易管理列表
export function listTrade(query) {
  return request({
    url: '/system/trade/list',
    method: 'get',
    params: query
  })
}

// 查询交易管理详细
export function getTrade(id) {
  return request({
    url: '/system/trade/' + id,
    method: 'get'
  })
}

// 新增交易管理
export function addTrade(data) {
  return request({
    url: '/system/trade',
    method: 'post',
    data: data
  })
}

// 修改交易管理
export function updateTrade(data) {
  return request({
    url: '/system/trade',
    method: 'put',
    data: data
  })
}

// 删除交易管理
export function delTrade(id) {
  return request({
    url: '/system/trade/' + id,
    method: 'delete'
  })
}
