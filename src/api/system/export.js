import request from '@/utils/request'

// 查询人参产品出口管理列表
export function listExport(query) {
  return request({
    url: '/system/export/list',
    method: 'get',
    params: query
  })
}

// 查询人参产品出口管理详细
export function getExport(id) {
  return request({
    url: '/system/export/' + id,
    method: 'get'
  })
}

// 新增人参产品出口管理
export function addExport(data) {
  return request({
    url: '/system/export',
    method: 'post',
    data: data
  })
}

// 修改人参产品出口管理
export function updateExport(data) {
  return request({
    url: '/system/export',
    method: 'put',
    data: data
  })
}

// 删除人参产品出口管理
export function delExport(id) {
  return request({
    url: '/system/export/' + id,
    method: 'delete'
  })
}
