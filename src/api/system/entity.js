import request from '@/utils/request'

// 查询销售主体列表
export function listEntity(query) {
  return request({
    url: '/system/entity/list',
    method: 'get',
    params: query
  })
}

// 查询销售主体详细
export function getEntity(id) {
  return request({
    url: '/system/entity/' + id,
    method: 'get'
  })
}

// 新增销售主体
export function addEntity(data) {
  return request({
    url: '/system/entity',
    method: 'post',
    data: data
  })
}

// 修改销售主体
export function updateEntity(data) {
  return request({
    url: '/system/entity',
    method: 'put',
    data: data
  })
}

// 删除销售主体
export function delEntity(id) {
  return request({
    url: '/system/entity/' + id,
    method: 'delete'
  })
}
