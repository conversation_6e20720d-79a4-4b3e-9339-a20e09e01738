import request from '@/utils/request';

// 获取环境数据
export function getEntityCount() {
  return request({
    url: '/big_screen_first/subject_count',
    method: 'get'
  });
}

// 获取智慧监测
export function getAreaStats() {
  return request({
    url: '/big_screen_first/area_count',
    method: 'get'
  });
}

// 获取气象数据走势
export function getGinsengInventory() {
  return request({
    url: '/big_screen_first/stock_count',
    method: 'get'
  });
}

// 获取生态环境分析
export function getPlantingTasks() {
  return request({
    url: '/big_screen_first/task_count',
    method: 'get'
  });
}

// 获取设备在线率统计
export function getTechEmpowerment() {
  return request({
    url: '/big_screen_first/ability_count',
    method: 'get'
  });
}

// 获取设备离线告警
export function getTransactionTrend() {
  return request({
    url: '/big_screen_first/trade_trend',
    method: 'get'
  });
}

// 获取异常预警
export function getProcessingStatus() {
  return request({
    url: '/big_screen_first/product_process',
    method: 'get'
  });
}
