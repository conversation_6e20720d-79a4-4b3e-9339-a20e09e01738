<template>
  <div class="pictureImgxx">
    <!-- 按钮 -->
    <div class="floatingBtn float-animation" @click="openChatPanel">
      <img src="@/assets/images/Floating.gif" />
      <h2>参管家</h2>
    </div>
    <!-- 按钮 -->
    <!-- 聊天面板 -->
    <ChatPanel ref="chatPanel" :config="chatConfig" :showTriggerButton="false" />
    <!-- 标题 -->
    <ScrennHeader />
    <!-- 标题 -->
    <!-- 内容展示 -->
    <div class="viewport">
      <div class="columnLeft">
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>主体数量统计</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <BarChart :config="chartData.entityCount" />
          </div>
        </div>
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>宜参面积统计</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <div :style="{ marginTop: viewportHeight === 1080 ? '30px' : '0' }">
              <CircularChart :config="chartData.areaStats" />
            </div>
          </div>
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>人参存量统计</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <div :style="{ marginTop: viewportHeight === 1080 ? '30px' : '10px' }">
              <RoseChart :config="chartData.ginsengInventory" />
            </div>
          </div>
        </div>
      </div>
      <div class="columnCenter">
        <div class="columnCenterTop">
          <!-- 新加 -->
          <!-- <ul class="firstTop">
            <li>
              <div>
                <img src="@/assets/images/f1.png" style="width: 30px; height: 30px" />
                <p>{{ 4000 }}<span style="font-size: 12px">万株</span></p>
                <h2>年种植人参</h2>
              </div>
            </li>
            <li>
              <div>
                <img src="@/assets/images/f2.png" style="width: 30px; height: 30px" />
                <p>{{ (centerStats.factoryProductQuantity * 1) / 10000 }}<span style="font-size: 12px">万株</span></p>
                <h2>年加工人参</h2>
              </div>
            </li>
            <li>
              <div>
                <img src="@/assets/images/f3.png" style="width: 30px; height: 30px" />
                <p>{{ (centerStats.tradeQuantity * 1) / 10000 }}<span style="font-size: 12px">万株</span></p>
                <h2>年销售人参</h2>
              </div>
            </li>
            <li>
              <div>
                <img src="@/assets/images/f4.png" style="width: 30px; height: 30px" />
                <p>
                  {{ centerStats.deviceCount }}<span style="font-size: 12px">个</span>
                </p>
                <h2>年接入设备</h2>
              </div>
            </li>

            <li>
              <div>
                <img src="@/assets/images/f5.png" style="width: 30px; height: 30px" />
                <p>{{ centerStats.bankLendingQuantity }} <span style="font-size: 12px">笔</span></p>
                <h2>年授信笔数</h2>
              </div>
            </li>
          </ul> -->
          <!-- 新加 -->
          <div class="imgbox">
            <div class="imgTop">
              <img
                class="square"
                src="@/assets/images/diqiu.png"
                style="width: 280px; height: 280px; position: absolute; top: 23%; left: 12.1%"
              />
              <img class="renshen" src="@/assets/images/renshen.png" :style="{ marginTop: viewportHeight === 1080 ? '-20px' : '0' }"/>
            </div>
            <div class="imgBottom">
              <img class="dizuo" src="@/assets/images/dz.png" />
            </div>
          </div>
          <div class="iconBox">
            <ul class="iconLeft">
              <li>
                <div class="text">
                  <p>主体数量</p>
                  <h2>{{ centerStats.subjectCount }}<i>个</i></h2>
                </div>
                <img class="square" src="@/assets/images/yuanquan.png" />
              </li>
              <li>
                <div class="text">
                  <p>基地面积</p>
                  <h2>{{ centerStats.area }}<i>公顷</i></h2>
                </div>
                <img class="square" src="@/assets/images/yuanquan.png" />
              </li>
              <li>
                <div class="text">
                  <p>人参存量</p>
                  <h2>{{ centerStats.byl }}<i>亿株</i></h2>
                </div>
                <img class="square" src="@/assets/images/yuanquan.png" />
              </li>
            </ul>
            <div class="iconCenter">
              <img class="float-animation" src="@/assets/images/wj.png"  :style="{ marginTop: viewportHeight === 1080 ? '25px' : '10px' }"/>
            </div>
            <ul class="iconRight">
              <li>
                <div class="text">
                  <p>年销售额</p>
                  <h2>{{ (centerStats.orderAmount / 10000).toFixed(3) }}<i>亿元</i></h2>
                </div>
                <img class="square" src="@/assets/images/yuanquan.png" />
              </li>
              <li>
                <div class="text">
                  <p>年总产值</p>
                  <h2>{{ centerStats.cl }}<i>亿元</i></h2>
                </div>
                <img class="square" src="@/assets/images/yuanquan.png" />
              </li>
              <li>
                <div class="text">
                  <p>贷款总额</p>
                  <h2>{{ centerStats.lendingCount }}<i>亿元</i></h2>
                </div>
                <img class="square" src="@/assets/images/yuanquan.png" />
              </li>
            </ul>
          </div>
        </div>
        <div class="columnCenterBottom">
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>种植标准任务</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <LineChart :config="chartData.plantingTasks" />
            </div>
          </div>
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>科技赋能情况</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <BubbleChart :config="chartData.techEmpowerment" />
            </div>
          </div>
        </div>
      </div>
      <div class="columnRight">
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>交易数据走势</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <LineBar :config="chartData.transactionTrend" />
          </div>
        </div>
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>参产品加工情况</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <RadarChart :config="chartData.processingStatus" />
          </div>
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>金融助力情况</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <BarChart2 :config="chartData.financialSupport" />
          </div>
        </div>
      </div>
    </div>
    <!-- 内容展示 -->
    <!-- 边框线 -->
    <div class="pictureSix"></div>
    <!-- 边框线 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue';
import ScrennHeader from '@/components/ScrennHeader';
import BarChart from './components/BarChart.vue';
import BarChart2 from './components/BarChart2.vue';
import BubbleChart from './components/BubbleChart.vue';
import RadarChart from './components/RadarChart.vue';
import LineChart from './components/LineChart.vue';
import LineBar from './components/LineBar.vue';
import CircularChart from './components/CircularChart.vue';
import RoseChart from './components/RoseChart.vue';
import ChatPanel from '../../components/ChatPanel/index.vue';

import {
  getEntityCount,
  getAreaStats,
  getGinsengInventory,
  getPlantingTasks,
  getTechEmpowerment,
  getTransactionTrend,
  getProcessingStatus,
  getFinancialSupport,
  getHomeStats
} from '@/api/screen';

//页面高度
const viewportHeight = ref(0);
const updateViewportHeight = () => {
  viewportHeight.value = window.innerHeight;
};
// ChatPanel 配置
const chatConfig = ref({
  ip: '************',
  title: '参管家',
  wsPort: 8282,
  httpPort: 8383,
  showTriggerButton: false
});

// 获取组件引用
const chatPanel = ref(null);

// 中心区域统计数据
const centerStats = ref({
  entityCount: 0,
  baseArea: 0,
  ginsengStock: 0,
  annualSales: 0,
  annualOutput: 0,
  totalLoan: 0,
  deviceCount: 0,
  bankLendingQuantity: 0,
  orderAmount: 0,
  cl: 0,
  lendingCount: 0,
  factoryProductQuantity: 0,
  tradeQuantity: 0
});

// 各个图表的数据
const chartData = reactive({
  // 主体数量统计
  entityCount: {
    data: []
  },
  // 宜参面积统计
  areaStats: {
    data: []
  },
  // 人参存量统计
  ginsengInventory: {
    data: []
  },
  // 种植标准任务
  plantingTasks: {
    data: {}
  },
  // 科技赋能情况
  techEmpowerment: {
    data: []
  },
  // 交易数据走势
  transactionTrend: {
    data: []
  },
  // 参产品加工情况
  processingStatus: {
    data: []
  },
  // 金融助力情况
  financialSupport: {
    data: []
  }
});

// 初始化所有图表数据
const initChartData = async () => {
  // 获取主体数量统计
  const entityRes = await getEntityCount();
  if (entityRes.code === 200) {
    chartData.entityCount.data = entityRes.data;
  }

  // // 获取宜参面积统计
  // const areaRes = await getAreaStats();
  // if (areaRes.code === 200) {
  //   chartData.areaStats.data = areaRes.data.map((item) => ({
  //     name: item.name,
  //     value: item.value.toFixed(2)
  //   }));
  // }
  // 获取中心统计数据
  // const statsRes = await getHomeStats();
  // if (statsRes.code === 200) {
  //   centerStats.value = statsRes.data;
  // }

  // 获取人参存量统计
  const inventoryRes = await getGinsengInventory();
  if (inventoryRes.code === 200) {
    chartData.ginsengInventory.data = inventoryRes.data.map((item) => ({
      name:
        item.nf == 0
          ? '1-5年'
          : item.nf == 1
            ? '6-10年'
            : item.nf == 2
              ? '10-15年'
              : item.nf == 3
                ? '16-20年'
                : item.nf == 4
                  ? '20-30年'
                  : '30年以上',
      value: item.rs_count
    }));
  }

  // 获取种植标准任务
  const tasksRes = await getPlantingTasks();
  if (tasksRes.code === 200) {
    chartData.plantingTasks.data = tasksRes.data;
  }

  // 获取科技赋能情况
  const techRes = await getTechEmpowerment();
  if (techRes.code === 200) {
    chartData.techEmpowerment.data = techRes.data;
  }

  // 获取交易数据走势
  const transactionRes = await getTransactionTrend();
  if (transactionRes.code === 200) {
    chartData.transactionTrend.data = transactionRes.data.map((item) => ({
      month: item.month,
      order_count: item.order_count,
      total_amount: item.total_amount
    }));
  }

  // 获取参产品加工情况
  const processingRes = await getProcessingStatus();
  if (processingRes.code === 200) {
    chartData.processingStatus.data = processingRes.data.map((item) => ({
      year: item.year,
      value: item.products
    }));
  }

  // 获取金融助力情况
  const financialRes = await getFinancialSupport();
  if (financialRes.code === 200) {
    chartData.financialSupport.data = financialRes.data.map((item) => ({
      name: item.credit_name,
      value: item.credit_count
    }));
  }
  const homeStatsRes = await getHomeStats();
  if (homeStatsRes.code === 200) {
    centerStats.value = homeStatsRes.data;

    let num = homeStatsRes.data.lendingCount / 10000;
    centerStats.value.lendingCount = num.toFixed(2);
  }
};

//宜参面积统计
const areaChart = async () => {
  const areaRes = await getAreaStats();
  if (areaRes.code === 200) {
    chartData.areaStats.data = areaRes.data.map((item) => ({
      name: item.name,
      value: item.value.toFixed(2)
    }));
  }
};

// 打开面板方法
const openChatPanel = () => {
  chatPanel.value?.openPanel();
};

// 组件挂载时初始化数据
onMounted(() => {
  initChartData();
  areaChart();
  // 设置定时刷新（如果需要）
  const timer = setInterval(
    () => {
      initChartData();
    },
    5 * 60 * 1000
  ); // 每5分钟刷新一次

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(timer);
  });
});

onMounted(() => {
  updateViewportHeight(); // 初始化视口高度
  window.addEventListener('resize', updateViewportHeight); // 监听窗口大小变化
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateViewportHeight); // 移除监听
});
</script>
<style lang="scss" scoped>
.firstTop {
  display: flex;
  justify-content: space-between;

  li {
    text-align: center;
    background: url('@/assets/images/u594.png') no-repeat;
    background-size: 100% 100%;
    width: 100px;
    height: 98px;

    h2 {
      margin: 0;
      font-weight: normal;
      margin-top: 10px;
    }

    p {
      margin-top: 5px;
      font-weight: bold;
    }
  }

  li:nth-child(1) {
    // background: url('@/assets/images/first01.png') no-repeat;
    // background-size: 100% 100%;
    p {
      font-size: 18px;
      color: #ffbe95;
    }

    h2 {
      font-size: 14px;
      color: #fff;
    }
  }

  li:nth-child(2) {
    // background: url('@/assets/images/first02.png') no-repeat;
    // background-size: 100% 100%;
    p {
      font-size: 18px;
      color: #ffbe95;
      color: #4fe4cb;
    }

    h2 {
      font-size: 14px;
      color: #fff;
    }
  }

  li:nth-child(3) {
    // background: url('@/assets/images/first03.png') no-repeat;
    // background-size: 100% 100%;
    p {
      font-size: 18px;
      color: #58a6ff;
    }

    h2 {
      font-size: 14px;
      color: #fff;
    }
  }

  li:nth-child(4) {
    // background: url('@/assets/images/first04.png') no-repeat;
    // background-size: 100% 100%;
    p {
      font-size: 18px;
      color: #ffc760;
    }

    h2 {
      font-size: 14px;

      color: #fff;
    }
  }

  li:nth-child(5) {
    // background: url('@/assets/images/first05.png') no-repeat;
    // background-size: 100% 100%;
    p {
      font-size: 18px;
      color: #eaa0d9;
    }

    h2 {
      font-size: 14px;
      color: #fff;
    }
  }
}
</style>
