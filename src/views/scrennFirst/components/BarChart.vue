<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup type="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

// 定义props
const props = defineProps({
  // 图表配置
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});

// props.config.data.countList
//监听数据
watch(
  () => props.config.data,
  () => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let resizeTimeout = null;

// 初始化图表
const initChart = () => {
  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);
  // 图表配置
  const option = {
    grid: {
      left: '15%',
      right: '10%',
      top: '15%',
      bottom: '15%'
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: (params) => {
        return `${params[0].name}:\n${params[0].value}个`;
      },
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
      }
    },
    xAxis: {
      data: props.config.data.nameList,
      //坐标轴
      axisLine: {
        lineStyle: {
          color: '#3eb2e8'
        }
      },
      //坐标值标注
      axisLabel: {
        show: true,
        textStyle: {
          color: '#74D5FF'
        }
      }
    },
    yAxis: {
      name: '单位:个',
      //坐标轴
      axisLine: {
        show: true,
        lineStyle: {
          color: '#3eb2e8'
        }
      },
      //坐标值标注
      axisLabel: {
        show: true,
        textStyle: {
          color: '#74D5FF'
        }
      },
      //分格线
      splitLine: {
        lineStyle: {
          color: 'rgba(66, 192, 255, .3)'
        }
      }
    },
    series: [
      {
        label: {
        show: true, // 开启显示数值标签
        position: 'top', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
        textStyle: {
          color: '#74D5FF', // 标签文字颜色，可按需调整
          fontSize: 12 // 标签文字大小，可按需调整
        }
      },
        name: 'a',
        tooltip: {
          show: false
        },
        type: 'bar',
        barWidth: 24.5,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              1,
              0,
              0,
              [
                {
                  offset: 0,
                  color: '#0B4EC3' // 0% 处的颜色
                },
                {
                  offset: 0.6,
                  color: '#138CEB' // 60% 处的颜色
                },
                {
                  offset: 1,
                  color: '#74D5Ff' // 100% 处的颜色
                }
              ],
              false
            )
          }
        },
        data: props.config.data.countList,
        barGap: 0
      },
      {
        type: 'bar',
        barWidth: 8,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              1,
              0,
              0,
              [
                {
                  offset: 0,
                  color: '#09337C' // 0% 处的颜色
                },
                {
                  offset: 0.6,
                  color: '#0761C0' // 60% 处的颜色
                },
                {
                  offset: 1,
                  color: '#74D5Ff' // 100% 处的颜色
                }
              ],
              false
            )
          }
        },
        barGap: 0,
        data: props.config.data.countList
      },
      {
        name: 'b',
        tooltip: {
          show: false
        },
        type: 'pictorialBar',
        itemStyle: {
          borderWidth: 1,
          borderColor: '#0571D5',
          color: '#1779E0'
        },
        symbol: 'path://M 0,0 l 120,0 l -30,60 l -120,0 z',
        symbolSize: ['0', '11'],
        symbolOffset: ['0', '11'],
        //symbolRotate: -5,
        symbolPosition: 'end',
        data: props.config.data.countList,
        z: 3
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 10000);
};

onMounted(() => {
  initChart(); // 首次初始化
  resizeChart();

  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>
