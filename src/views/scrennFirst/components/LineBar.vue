<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
const order_count = ref([]);
const total_amount = ref([]);

//监听数据变化
watch(
  () => props.config,
  (newVal) => {
    if (newVal.data) {
      order_count.value = [];
      total_amount.value = [];
      newVal.data.map((item) => {
        order_count.value.push(item.order_count);
        total_amount.value.push((item.total_amount * 1) / 1000);
      });
      setTimeout(() => {
        initChart();
      }, 1000);
    }
  },
  { deep: true, immediate: true }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '1%',
      top: '15%',
      containLabel: true
    },
    // legend: {
    //   data: ['工单数', '增长率'],
    //   bottom: '3%',
    //   right: '40%',
    //   textStyle: {
    //     color: '#74D5FF'
    //   },
    //   itemWidth: 12,
    //   itemHeight: 12
    // },
    xAxis: {
      type: 'category',
      data: props.config.data.map((item) => item.month),
      axisLabel: {
        color: '#74D5FF',
        fontSize: 10,
        interval: 0
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '单位:笔',
        nameTextStyle: {
          fontSize: 12,
          color: '#74D5FF'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(66, 192, 255, .3)'
          }
        }
      },
      {
        type: 'value',
        name: '单位:万元',
        interval: 4000,
        axisLabel: {
          formatter: '{value}'
        },
        nameTextStyle: {
          fontSize: 12,
          color: '#74D5FF'
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#74D5FF',
          fontSize: 12
        }
      }
    ],
    series: [
      {
        name: '单数',
        type: 'line',
        tooltip: {
          valueFormatter: function (value) {
            return value + '笔';
          }
        },
        data: order_count.value,
        lineStyle: { color: '#666ec1', width: 2 },
        symbol: 'circle',
        symbolSize: 6,
        yAxisIndex: 0, // 关联第一个 y 轴（cm）
        smooth: 0.6
      },
      {
        name: '交易额',
        type: 'bar',
        data: total_amount.value,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(33,75, 76, 1)' },
            { offset: 1, color: 'rgba(20, 66, 99, 0.8)' }
          ])
        },
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' 万元';
          }
        },
        barWidth: '40%'
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 10000);
};

onMounted(() => {
  initChart();
  resizeChart();
  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
