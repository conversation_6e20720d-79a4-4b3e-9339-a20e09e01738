<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

// 定义props
const props = defineProps({
  // 图表配置
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});

// 监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  const trafficWay = props.config.data;

  const color = ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000'];
  const data = [];

  for (let i = 0; i < trafficWay.length; i++) {
    data.push(
      {
        value: trafficWay[i].value,
        name: trafficWay[i].name,
        itemStyle: {
          normal: {
            borderWidth: 2,
            shadowBlur: 20,
            borderColor: color[i],
            shadowColor: color[i]
          }
        }
      },
      {
        value: 2,
        name: '',
        itemStyle: {
          normal: {
            label: { show: false },
            labelLine: { show: false },
            color: 'rgba(0, 0, 0, 0)',
            borderColor: 'rgba(0, 0, 0, 0)',
            borderWidth: 0
          }
        }
      }
    );
  }

  const seriesOption = [
    {
      name: '',
      type: 'pie',
      clockWise: false,
      radius: [50, 60],
      center: ['50%', '50%'],
      hoverAnimation: false,
      itemStyle: {
        borderRadius: 8
      },
      label: {
        show: true,
        color: '#74D5FF',
        // 关键修改：确保单位完整显示
        position: 'outside',
        overflow: 'none', // 不截断文本
        ellipsis: '', // 不显示省略号
        fontSize: 12, // 适当增大字体确保显示完整
        lineHeight: 1.5,
        formatter: function (params) {
          // 确保单位"万株"完整显示
          return params.name ? `${params.name}: ${params.value}万株` : '';
        }
      },
      // 调整标签线，避免遮挡文本
      labelLine: {
        length: 20,
        length2: 30,
        lineStyle: {
          width: 1
        }
      },
      data: data
    }
  ];

  // 图表配置，新增graphic配置项用于放置中心图片
  const option = {
    color: color,
    tooltip: {
      show: true,
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 12
      },
      formatter: (params) => {
        return `${params.name}:\n${params.value}万株`;
      }
    },
    toolbox: {
      show: false
    },
    series: seriesOption,
    // 中心图片配置
    graphic: {
      elements: [
        {
          type: 'image',
          z: 100, // 确保图片在最上层
          left: '38%',
          top: 'center',
          style: {
            image: new URL('/src/assets/images/rs.png', import.meta.url).href,
            width: 100, // 图片宽度，根据需要调整
            height:100, // 图片高度，根据需要调整
            borderRadius: '50%' // 可选：如果需要圆形图片
          }
        }
      ]
    }
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  reloadTimer = setInterval(initChart, 12000);
};

onMounted(() => {
  resizeChart();
  initChart();
  window.addEventListener('resize', handleResize);
  startReloadTimer();
});

onBeforeUnmount(() => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
  height: 200px;
}
</style>
