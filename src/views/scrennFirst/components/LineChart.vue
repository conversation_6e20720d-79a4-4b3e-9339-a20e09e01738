<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

// 定义props
const props = defineProps({
  // 图表配置
  config: {
    type: Object,
    default: () => ({
      data: {
        harvestList: [],
        monthList: [],
        patrolList: [],
        seedList: [],
        siteList: [],
        weedList: []
      },
      autoRefresh: true,
      refreshInterval: 3000
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  const option = {
    color: ['#19d9fe', '#1978e6', '#20bc8e', '#e9cc10', '#a24747', '#6b47a2'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: (params) => {
        // params 是数组，每个系列一个
        let str = params[0].axisValueLabel + '<br/>';
        params.forEach((item) => {
          str += `${item.seriesName}：${item.value}个<br/>`;
        });
        return str;
      }
    },
    legend: {
      data: ['选地', '播种', '收参', '除草', '收种', '巡地'],
      textStyle: {
        color: '#74D5FF'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%', // 适当增加底部距离，避免标签被截断
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.config.data.monthList,
      axisLine: {
        lineStyle: {
          color: 'rgba(66, 192, 255, .3)'
        }
      },
      axisLabel: {
        color: '#05D5FF',
        textStyle: {
          fontSize: 12
        },
        interval: 0, // 关键配置：强制显示所有标签（0表示全部显示）
        // 可选：如果标签过挤，可设置旋转角度
        rotate: 0 // 0-90度，根据需要调整（例如rotate: 30）
      },
      axisTick: {
        // 刻度
        show: false
      }
    },
    yAxis: [
      {
        axisLabel: {
          formatter: '{value}',
          color: '#74D5FF',
          textStyle: {
            fontSize: 12
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(66, 192, 255, .3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(66, 192, 255, .3)'
          }
        }
      }
    ],
    series: [
      {
        name: '选地',
        type: 'line',
        data: props.config.data.siteList,
        smooth: 0.5,
        symbol: 'circle'
      },
      {
        name: '播种',
        type: 'line',
        data: props.config.data.seedList,
        smooth: 0.5,
        symbol: 'circle'
      },
      {
        name: '收参',
        type: 'line',
        data: props.config.data.collectList,
        smooth: 0.5,
        symbol: 'circle'
      },
      {
        name: '除草',
        type: 'line',
        data: props.config.data.weedList,
        smooth: 0.5,
        symbol: 'circle'
      },
      {
        name: '收种',
        type: 'line',
        data: props.config.data.harvestList,
        smooth: 0.5,
        symbol: 'circle'
      },
      {
        name: '巡地',
        type: 'line',
        data: props.config.data.patrolList,
        smooth: 0.5,
        symbol: 'circle'
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 13000);
};

onMounted(() => {
  initChart(); // 首次初始化
  resizeChart();

  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
