<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import 'echarts-gl'; // 引入3D扩展

// 接收父组件传入的配置
const props = defineProps({
  config: {
    type: Object,
    default: () => ({ data: [] })
  }
});

// 响应式变量
const chartContainer = ref(null);
let chartInstance = null;
const localData = ref([]);
const dataList = ref([]);
let reloadTimer = null; // 定时器实例

// 颜色映射配置
const colorMap = {
  '耕地已种植面积': 'rgba(6, 189, 205, 0.5)',
  '耕地未种植面积': 'rgba(255, 196, 0, 0.5)',
  '耕地面积': 'rgba(67, 195, 106, 0.8)',
  '林地已种植面积': 'rgba(6, 189, 205, 0.5)',
  '林地未种植面积': 'rgba(255, 196, 0, 0.5)',
  '耕地面积': 'rgba(67, 195, 106, 0.8)',
  default: 'rgba(150, 150, 150, 0.5)'
};

// 1. 数据转换函数
function convertDataFormat(data) {
  return data.map(item => {
    const { name, value } = item;
    const val = parseFloat(value);
    const color = colorMap[name] || colorMap.default;
    return { name, val, itemStyle: { color } };
  });
}

// 2. 生成扇形曲面参数方程
const heightProportion = 0.2; // 整体高度比例（可缩小）
const maxDataHeight = 7.5; // 单个数据的最大高度限制 

function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height) {
  let midRatio = (startRatio + endRatio) / 3;
  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;

  if (startRatio === 0 && endRatio === 1) isSelected = false;
  k = k ?? 1 / 3;
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  let hoverRate = isHovered ? 1.1 : 1;

  return {
    u: { min: -Math.PI, max: Math.PI * 3, step: Math.PI / 32 },
    v: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
    x: function (u, v) {
      if (u < startRadian) return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      if (u > endRadian) return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u, v) {
      if (u < startRadian) return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      if (u > endRadian) return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u, v) {
      if (u < -Math.PI * 0.5 || u > Math.PI * 2.5) return Math.sin(u);
      // 应用高度限制
      const calculatedHeight = heightProportion * height;
      const limitedHeight = Math.min(calculatedHeight, maxDataHeight);
      return Math.sin(v) > 0 ? limitedHeight : -1;
    }
  };
}

// 3. 生成3D饼图系列配置
function getPie3D(pieData, internalDiameterRatio) {
  let series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  let linesSeries = [];
  const k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3;

  pieData.forEach(item => sumValue += item.value);

  pieData.forEach((item, i) => {
    endValue = startValue + item.value;
    item.startRatio = startValue / sumValue;
    item.endRatio = endValue / sumValue;
    series.push({
      name: item.name,
      type: 'surface',
      parametric: true,
      wireframe: { show: false },
      pieData: item,
      pieStatus: { selected: false, hovered: false, k },
      itemStyle: item.itemStyle,
      parametricEquation: getParametricEquation(
        item.startRatio,
        item.endRatio,
        false,
        false,
        k,
        item.value
      )
    });

    // 生成label指示线
    const midRadian = (item.startRatio + item.endRatio) * Math.PI;
    const posX = Math.cos(midRadian) * (1 + Math.cos(Math.PI / 2));
    const posY = Math.sin(midRadian) * (1 + Math.cos(Math.PI / 2));
    const posZ = Math.log(Math.abs(item.value + 1)) * 0.1;
    const flag = (midRadian >= 0 && midRadian <= Math.PI / 2) || (midRadian >= 3 * Math.PI / 2 && midRadian <= Math.PI * 2) ? 1 : -1;
    const color = item.itemStyle.color;
    const turningPosArr = [posX * 1.8 + (i * 0.1 * flag) + (flag < 0 ? -0.5 : 0), posY * 1.8 + (i * 0.1 * flag) + (flag < 0 ? -0.5 : 0), posZ * 2];
    const endPosArr = [posX * 1.9 + (i * 0.1 * flag) + (flag < 0 ? -0.5 : 0), posY * 1.9 + (i * 0.1 * flag) + (flag < 0 ? -0.5 : 0), posZ * 6];

    linesSeries.push({
      type: 'line3D',
      lineStyle: { color },
      data: [[posX, posY, posZ], turningPosArr, endPosArr]
    }, {
      type: 'scatter3D',
      label: {
        show: true,
        position: 'center',
        textStyle: {
          color: '#fff',
          backgroundColor: color,
          borderWidth: 2,
          fontSize: 14,
          padding: 10,
          borderRadius: 4
        },
        formatter: params => {
          const [name, val] = params.name.split('\n');
          return `${name}\n${val} 公顷`;
        }
      },
      symbolSize: 0,
      data: [{ name: `${item.name}\n${item.val}`, value: endPosArr }]
    });

    startValue = endValue;
  });

  // 底部圆盘
  series.push({
    name: 'base',
    type: 'surface',
    parametric: true,
    wireframe: { show: false },
    itemStyle: { opacity: 1, color: 'rgba(25, 93, 176, 1)' },
    parametricEquation: {
      u: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
      v: { min: 0, max: Math.PI, step: Math.PI / 20 },
      x: (u, v) => ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2,
      y: (u, v) => ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2,
      z: (u, v) => Math.cos(v) > 0 ? -0 : -1.5
    }
  });

  return series.concat(linesSeries);
}

// 4. 初始化/更新图表（核心：从无到有动画）
function initOrUpdateChart(processedData) {
  if (!chartContainer.value || !processedData.length) return;

  // 1. 先销毁旧实例（制造“无”的状态）
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 2. 延迟300ms再渲染新图表（增强“从无到有”的视觉差）
  setTimeout(() => {
    // 计算占比
    const total = processedData.reduce((sum, item) => sum + item.val, 0);
    const pieData = processedData.map(item => ({
      ...item,
      value: Number(((item.val / total) * 100).toFixed(2))
    }));

    // 创建新实例并设置配置（添加强动画）
    chartInstance = echarts.init(chartContainer.value);
    chartInstance.setOption({
      animation: true,
      animationDuration: 1000, // 动画持续1秒
      animationEasing: 'elasticOut', // 弹性动画，增强“生长”感
      animationDelay: (idx) => idx * 150, // 每个扇形错开出现
      labelLine: { show: true, lineStyle: { color: '#7BC0CB' } },
      label: { show: false },
      xAxis3D: { min: -1.9, max: 1.5 },
      yAxis3D: { min: -1.5, max: 1.5 },
      zAxis3D: { min: -1, max: 1 },
      grid3D: {
        show: false,
        boxHeight: 4,
        bottom: '20%',
        viewControl: {
          distance: 105,
          alpha: 30,
          beta: 50, // 固定角度
          autoRotate: false,
          center: [0, 8, -0.2]
        }
      },
      series: getPie3D(pieData, 0.8)
    });
  }, 300);
}

// 5. 监听数据变化
watch(
  () => props.config.data,
  async (newData) => {
    if (newData && newData.length > 0) {
      const processedData = convertDataFormat(newData);
      dataList.value = processedData;
      await nextTick();
      initOrUpdateChart(processedData);
    }
  },
  { immediate: true }
);

// 窗口resize事件
function handleResize() {
  chartInstance?.resize();
}

// 6. 定时器（3秒刷新一次）
function startReloadTimer() {
  // 清除旧定时器
  if (reloadTimer) clearInterval(reloadTimer);
  
  // 每3秒强制刷新
  reloadTimer = setInterval(() => {
    if (dataList.value.length) {
      initOrUpdateChart(dataList.value);
    }
  }, 11000); // 3秒间隔
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance) chartInstance.dispose();
  if (reloadTimer) clearInterval(reloadTimer);
});
</script>

<style scoped>
.w-full { width: 100%; }
.h-full { height: 180px; }
</style>