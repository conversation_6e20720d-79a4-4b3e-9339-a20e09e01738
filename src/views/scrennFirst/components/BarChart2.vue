<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

const initChart = () => {
  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartContainer.value);
  // 图表数据
  let data = props.config.data;

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      axisPointer: { type: 'shadow' },
      // 提示框也同步添加单位，保持一致性
      formatter: '{b}: {c} 万元'
    },
    grid: {
      left: '15%',
      right: '15%',
      top: '15%',
      bottom: '15%'
    },
    yAxis: {
      // yAxis显示分类
      type: 'category',
      data: data.map((item) => item.name),
      axisLine: {
        lineStyle: { color: 'rgba(66, 192, 255, .3)' }
      },
      axisLabel: {
        color: '#74D5FF',
        fontSize: 12
      },
      axisTick: { show: false }
    },
    xAxis: {
      // xAxis显示数值（添加万元单位）、
      name: '万元',
      nameLocation: 'end', // 将名称放在坐标轴末尾
      nameGap: 10, // 调整名称与坐标轴的距离
      nameTextStyle: {
        color: '#74D5FF',
        fontSize: 12,
        align: 'right', // 文本右对齐
        verticalAlign: 'top', // 文本垂直靠上
        padding: [-140, -30, 0, -5] // 微调内边距
      },
      type: 'value',
      axisLabel: {
        // 关键修改：在数值后添加“万元”
        formatter: '{value}',
        color: '#74D5FF',
        fontSize: 12
      },
      axisTick: { show: false },
      axisLine: {
        lineStyle: { color: 'rgba(66, 192, 255, .3)' }
      },
      splitLine: {
        lineStyle: { color: 'rgba(66, 192, 255, .3)' }
      }
    },
    series: [
      {
        type: 'bar',
      label: {
        show: true, // 开启显示数值标签
        position: 'right', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
        textStyle: {
          color: '#74D5FF', // 标签文字颜色，可按需调整
          fontSize: 12 // 标签文字大小，可按需调整
        }
      },
        data: data.map((item) => item.value),
        barWidth: 15,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            1,
            0,
            0,
            0, // 横向渐变
            [
              { offset: 0, color: 'rgba(5, 213, 255, 1)' },
              { offset: 0.98, color: 'rgba(5, 213, 255, 0)' }
            ]
          ),
          shadowColor: '#74D5FF',
          shadowBlur: 4
        }
      }
    ]
  };

  chartInstance.setOption(option);
};

// 保留原有的resize和生命周期方法
const resizeChart = () => chartInstance?.resize();
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  resizeChart();
  window.addEventListener('resize', handleResize);
  reloadTimer = setInterval(initChart, 8000);
});

onBeforeUnmount(() => clearInterval(reloadTimer));
onUnmounted(() => {
  chartInstance?.dispose();
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
