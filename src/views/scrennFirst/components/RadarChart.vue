<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
const indicator1 = ref([]);
const indicator2 = ref([]);
const indicator3 = ref([]);
const serve1 = ref([]);
const serve2 = ref([]);
const serve3 = ref([]);
//监听数据变化
watch(
  () => props.config.data,
  (newValue) => {
    indicator1.value = [];
    indicator2.value = [];
    indicator3.value = [];
    serve1.value = [];
    serve2.value = [];
    serve3.value = [];
    newValue[0].value.map((item) => {
      indicator1.value.push({ name: item.product_name, max: item.product_count });
    });
    newValue[1].value.map((item) => {
      indicator2.value.push({ name: item.product_name, max: item.product_count });
    });
    newValue[2].value.map((item) => {
      indicator3.value.push({ name: item.product_name, max: item.product_count });
    });
    indicator1.value.map((item) => {
      serve1.value.push(item.max);
    });
    indicator2.value.map((item) => {
      serve2.value.push(item.max);
    });
    indicator3.value.map((item) => {
      serve3.value.push(item.max);
    });

    initChart();
  },
  {
    deep: true
  }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

const initChart = () => {
  let yearList = [];
  props.config.data.map((item) => {
    yearList.push(item.year);
  });

  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
  }
  let product_name = [];
  let nameList = [];
  props.config.data[0].value.map((item) => {
    product_name.push({ name: item.product_name, max: 100 });
    nameList.push(item.product_name);
  });

  chartInstance = echarts.init(chartContainer.value);
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: function (params) {
        // 获取 option 里的 indicator
        const indicators = params?.indicatorAxes ? params.indicatorAxes.map((item) => item.name) : nameList; // 兜底
        let str = params.name + '<br/>';
        if (Array.isArray(params.value)) {
          params.value.forEach((v, i) => {
            str += indicators[i] + ': ' + v + '%<br/>';
          });
        }
        return str;
      }
    },
    legend: {
      data: yearList,
      orient: 'vertical',
      right: 0,
      bottom: 'center',
      textStyle: {
        color: '#74D5FF'
      },
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8
    },
    radar: {
      indicator: product_name,
      shape: 'polygon',
      splitNumber: 4,
      center: ['40%', '50%'],
      radius: '70%',
      nameGap: 15,
      splitArea: {
        areaStyle: {
          color: [
            'rgba(255, 255, 255, 0.02)',
            'rgba(255, 255, 255, 0.05)',
            'rgba(255, 255, 255, 0.08)',
            'rgba(255, 255, 255, 0.11)'
          ]
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      name: {
        textStyle: {
          color: '#74D5FF',
          fontSize: 12
        }
      }
    },
    series: [
      {
        name: '人参加工情况',
        type: 'radar',
        data: [
          {
            value: serve1.value,
            name: yearList[0],
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 2,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#19d9fe' },
                { offset: 1, color: '#19d9fe' }
              ])
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(56, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(56, 144, 255, 0.1)' }
              ])
            },
            itemStyle: {
              color: '#19d9fe'
            }
          },
          {
            value: serve2.value,
            name: yearList[1],
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 2,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#e9cc10' },
                { offset: 1, color: '#e9cc10' }
              ])
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(233, 204, 16, 0.3)' },
                { offset: 1, color: 'rgba(233, 204, 16, 0.1)' }
              ])
            },
            itemStyle: {
              color: '#e9cc10'
            }
          },
          {
            value: serve3.value,
            name: yearList[2],
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 2,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#20bc8e' },
                { offset: 1, color: '#20bc8e' }
              ])
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(57, 251, 208, 0.3)' },
                { offset: 1, color: 'rgba(57, 251, 208, 0.1)' }
              ])
            },
            itemStyle: {
              color: '#20bc8e'
            }
          }
        ]
      }
    ]
  };

  chartInstance.setOption(option);
};

// 保留原有的resize和生命周期方法
const resizeChart = () => chartInstance?.resize();
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  nextTick(() => {
    resizeChart();
    window.addEventListener('resize', handleResize);
    reloadTimer = setInterval(initChart, 16000);
  });
});

onBeforeUnmount(() => clearInterval(reloadTimer));
onUnmounted(() => {
  chartInstance?.dispose();
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
