<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据变化
watch(props.config, () => {
  initChart();
});
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

// 生成渐变颜色配置的工具函数
const getGradientColor = (color) => {
  // 解析颜色为RGB值（简单处理十六进制颜色）
  const hexToRgb = (hex) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return { r, g, b };
  };

  const rgb = hexToRgb(color);
  // 返回渐变配置，从中心浅色到边缘原色
  return {
    type: 'radial', // 径向渐变（从中心向外）
    x: 0.5, // 渐变中心x坐标（0-1）
    y: 0.5, // 渐变中心y坐标（0-1）
    r: 0.8, // 渐变半径（0-1）
    colorStops: [
      { offset: 0, color: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.8)` }, // 中心颜色（稍浅）
      { offset: 1, color: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.4)` } // 边缘颜色（更透明）
    ],
    global: false // 只在当前图形中使用该渐变
  };
};

function wrapText(text, maxLen = 5) {
  if (!text) return '';
  let result = '';
  for (let i = 0; i < text.length; i += maxLen) {
    result += text.substr(i, maxLen) + '\n';
  }
  return result.trim();
}

const initChart = () => {
  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartContainer.value);
  //最多显示10个
  if (props.config.data.length > 10) {
    props.config.data = props.config.data.slice(0, 10);
  }
  var plantCap = props.config.data;

  // 重新设计位置分布，确保圆圈不重叠
  var datalist = [
    { offset: [50, 75], symbolSize: 95, opacity: 0.95, color: '#3ecd7e' }, // 绿色
    { offset: [25, 45], symbolSize: 70, opacity: 0.88, color: '#ffc327' }, // 黄色
    { offset: [15, 85], symbolSize: 80, opacity: 0.84, color: '#ff745b' }, // 橙红
    { offset: [65, 85], symbolSize: 90, opacity: 0.8, color: '#2392fe' }, // 蓝色
    { offset: [30, 0], symbolSize: 65, opacity: 0.75, color: '#a259ec' }, // 紫色
    { offset: [70, 15], symbolSize: 70, opacity: 0.7, color: '#ff6f91' }, // 粉色
    { offset: [35, 80], symbolSize: 60, opacity: 0.68, color: '#43e8d8' }, // 青色
    { offset: [85, 50], symbolSize: 50, opacity: 0.6, color: '#f9c846' }, // 金色
    { offset: [15, 10], symbolSize: 60, opacity: 0.68, color: '#ff914d' }, // 橙色
    { offset: [55, 10], symbolSize: 60, opacity: 0.68, color: '#ff914d' } // 橙色
  ];

  var datas = [];
  // 计算最大最小值用于缩放
  let minNum = Infinity,
    maxNum = -Infinity;
  plantCap.forEach((item) => {
    let n = item.num !== undefined ? item.num : item.value;
    if (n !== undefined) {
      minNum = Math.min(minNum, n);
      maxNum = Math.max(maxNum, n);
    }
  });
  // 定义气泡大小范围
  const minSize = 50,
    maxSize = 80;
  for (var i = 0; i < plantCap.length; i++) {
    var item = plantCap[i];
    // datalist 不够时用最后一个样式
    var baseStyle = datalist[i] !== undefined ? datalist[i] : datalist[datalist.length - 1];
    let n = item.num !== undefined ? item.num : item.value;
    // 计算 symbolSize
    let symbolSize = baseStyle.symbolSize;
    if (n !== undefined && maxNum > minNum) {
      symbolSize = minSize + ((maxSize - minSize) * (n - minNum)) / (maxNum - minNum);
    } else if (n !== undefined) {
      symbolSize = (minSize + maxSize) / 2;
    }
    datas.push({
      name: item.name,
      value: baseStyle.offset, // 直接用固定位置
      symbolSize: symbolSize,
      label: {
        normal: {
          show: true,
          formatter: wrapText(item.name, 5), // 每5个字换一行
          color: '#fff',
          textStyle: {
            fontSize: Math.max(12, Math.min(14, symbolSize / 8)),
            fontWeight: 'bold'
          },
          position: 'inside'
        }
      },
      itemStyle: {
        normal: {
          color: getGradientColor(baseStyle.color),
          opacity: baseStyle.opacity,
          borderColor: baseStyle.color,
          borderWidth: 1
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 15,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          show: true,
          position: 'inside',
          textStyle: {
            fontSize: Math.max(12, Math.min(14, symbolSize / 8)),
            fontWeight: 'bold'
          }
        }
      }
    });
  }

  const option = {
    grid: {
      show: false,
      top: '16%',
      left: 10,
      right: 10,
      bottom: '15%',
      containLabel: true
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: function (params) {
        // params.data.name 是名称，params.data.raw/params.data.num/params.data.value 可能是数量
        // 假设 props.config.data[i] 里有 num 字段
        let name = params.data.name || '';
        let num = '';
        if (props.config.data && Array.isArray(props.config.data)) {
          const found = props.config.data.find((item) => item.name === name);
          if (found && (found.num !== undefined || found.value !== undefined)) {
            num = found.num !== undefined ? found.num : found.value;
          }
        }
        return `名称：${name}<br>数量：${num}`;
      }
    },
    xAxis: [
      {
        gridIndex: 0,
        type: 'value',
        show: false,
        min: 0,
        max: 100,
        nameLocation: 'middle',
        nameGap: 5
      }
    ],
    yAxis: [
      {
        gridIndex: 0,
        min: 0,
        show: false,
        max: 100,
        nameLocation: 'middle',
        nameGap: 30
      }
    ],
    series: [
      {
        type: 'scatter',
        symbol: 'circle',
        label: {
          normal: {
            show: true,
            formatter: '{b}',
            color: '#fff',
            textStyle: {
              fontSize: '14',
              fontWeight: 'bold'
            },
            position: 'inside'
          }
        },
        data: datas
      }
    ]
  };

  chartInstance.setOption(option);
};

// 保留原有的resize和生命周期方法
const resizeChart = () => chartInstance?.resize();
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  // nextTick(() => {
  initChart();
  resizeChart();
  window.addEventListener('resize', handleResize);
  // reloadTimer = setInterval(initChart, 3000);
  // });
});

onBeforeUnmount(() => clearInterval(reloadTimer));
onUnmounted(() => {
  chartInstance?.dispose();
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
