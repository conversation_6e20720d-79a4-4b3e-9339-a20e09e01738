<template>
  <div class="pictureImg">
    <!-- 按钮 -->
    <div class="floatingBtn float-animation" @click="openChatPanel">
      <img src="@/assets/images/Floating.gif" />
      <h2>参管家</h2>
    </div>
    <!-- 按钮 -->
    <!-- 聊天面板 -->
    <ChatPanel ref="chatPanel" :config="chatConfig" :showTriggerButton="false" />
    <!-- 标题 -->
    <ScrennHeader />
    <!-- 标题 -->
    <!-- 内容展示 -->
    <div class="viewport">
      <div class="columnLeft">
        <div class="columnItem">
          <div class="boxTitlex">
            <div class="Title">
              <h2><i></i>人参价格趋势</h2>
              <ul class="tablist">
                <li
                  v-for="(tab, index) in tabs"
                  :key="index"
                  @click="clickTab(index)"
                  :class="{ active: activeIndex === index }"
                >
                  {{ tab }}
                </li>
              </ul>
            </div>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <LineChart :config="chartData.lineChartConfig" />
          </div>
        </div>
        <div class="columnItem columnItemFive" style="margin-bottom: 0; height: 67%">
          <div class="boxTitle">
            <h2><i></i>人参销量排名</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox" style="height: 87%">
            <div class="fiveBox">
              <div class="fiveTop">
                <p>国内省份人参销售额排名TOP5</p>
                <ProgressChart :config="chartData.progressChartConfig1" />
              </div>
              <div class="fiveBottom">
                <p>出口国家人参销售额排名TOP5</p>
                <ProgressChart2 :config="chartData.progressChartConfig2" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="columnCenter">
        <div class="scrennSecondStyle" style="height: 9%">
          <ul class="scrennSecondData" style="height: 100%">
            <li style="width: 18%; justify-content: flex-start">
              <div class="icoBox">
                <img src="@/assets/images/ico6.png" class="float-animation" style="width: 40px; height: 40px" />
                <img src="@/assets/images/u907.png" style="width: 50px; margin-top: -30px" />
              </div>
              <div style="text-align: left">
                <h2 style="font-size: 12px; font-weight: normal">年交易额（万元）</h2>
                <p>{{ Topdata.tradeAmount }}</p>
              </div>
            </li>
            <li style="width: 18%">
              <div class="icoBox">
                <img src="@/assets/images/ico7.png" class="float-animation" style="width: 40px; height: 40px" />
                <img src="@/assets/images/u1297.png" style="width: 50px; margin-top: -30px" />
              </div>
              <div style="text-align: left">
                <h2 style="font-size: 12px; font-weight: normal">年交易量（万株）</h2>
                <p>{{ (Topdata.tradeQuantity * 1) / 10000 }}</p>
              </div>
            </li>
            <li style="width: 20%; justify-content: flex-start">
              <div class="icoBox">
                <img src="@/assets/images/ico8.png" class="float-animation" style="width: 40px; height: 40px" />
                <img src="@/assets/images/u1315.png" style="width: 50px; margin-top: -30px" />
              </div>
              <div style="text-align: left">
                <h2 style="font-size: 12px; font-weight: normal">年出口贸易额（万元）</h2>
                <p>{{ Topdata.exportAmount }}</p>
              </div>
            </li>
            <li style="width: 20%; justify-content: flex-start">
              <div class="icoBox">
                <img src="@/assets/images/ico9.png" class="float-animation" style="width: 40px; height: 40px" />
                <img src="@/assets/images/u1301.png" style="width: 50px; margin-top: -30px" />
              </div>
              <div style="text-align: left">
                <h2 style="font-size: 12px; font-weight: normal">年出口量（万株）</h2>
                <p>{{ Topdata.exportCount }}</p>
              </div>
            </li>
          </ul>
        </div>
        >

        <div class="columnCenterBottom" style="height: 29.2%">
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>人参产品交易额</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <RoseChart :config="chartData.roseChartConfig" />
            </div>
          </div>
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>交易额增长趋势图</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <LineBar2 :config="chartData.lineBar2Config" />
            </div>
          </div>
        </div>
        <div class="columnCenterBottom" style="height: 28%">
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>产业主体销售额排名</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox" style="height: 68%">
              <sort-table
                :data="dataList01"
                :scroll-delay="30"
                @custom-event="handleRowClick02"
                :style="{ height: tableHeight, transition: 'height 0.3s ease-in-out' }"
              >
                <el-table-column label="排名" align="center" width="50">
                  <template v-slot="scope">
                    <span v-if="scope.row.index == 1">
                      <img src="@/assets/images/one.png" height="20px" />
                    </span>
                    <span v-else-if="scope.row.index == 2">
                      <img src="@/assets/images/two.png" height="20px" />
                    </span>
                    <span v-else-if="scope.row.index == 3">
                      <img src="@/assets/images/three.png" height="20px" />
                    </span>
                    <span v-else>
                      {{ scope.row.index }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="产业主体" align="center">
                  <template v-slot="scope">
                    <span class="customSpan">{{ scope.row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="交易金额(万元)" align="center" width="120">
                  <template v-slot="scope">
                    <span class="customSpan">{{ scope.row.total_amount }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="交易笔数" align="center" width="100">
                  <template v-slot="scope">
                    <span class="customSpan">{{ scope.row.total_count }}</span>
                  </template>
                </el-table-column>
              </sort-table>
            </div>
          </div>
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>销售渠道占比(月)</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <ul class="historyList">
                <li>
                  <statisticsChart :config="chartData.statisticsChartConfig" />
                  <h2>
                    <span style="color: #ffec3d">{{ xsjy }}</span
                    >万元
                  </h2>
                  <h3>线上交易</h3>
                </li>
                <li>
                  <statisticsChart2 :config="chartData.statisticsChartConfig2" />
                  <h2>
                    <span style="color: #00e676">{{ xxjy }}</span
                    >万元
                  </h2>
                  <h3>线下交易</h3>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="columnCenterBottom" style="height: 28%">
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>出口规模及增速</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <LineBar :config="chartData.lineBarConfig" />
            </div>
          </div>
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>实时物流数据</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox" style="height: 68%">
              <auto-scroll-table :data="dataList02" :scroll-delay="30" @custom-event="handleRowClick">
                <el-table-column label="路线" align="center">
                  <template v-slot="scope">
                    <span class="customSpan">{{ scope.row.line }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="车牌" align="center">
                  <template v-slot="scope">
                    <span class="customSpan">{{ scope.row.car_number }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="运单号" align="center">
                  <template v-slot="scope">
                    <span class="customSpan">{{ scope.row.order_number }}</span>
                  </template>
                </el-table-column>
              </auto-scroll-table>
            </div>
          </div>
        </div>
      </div>
      <div class="columnRight">
        <div style="height: 66.2%; width: 100%; margin-bottom: 3.5%">
          <Swiper :config="chartData.swiperConfig" />
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>文旅客流&营收监测</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <LineBar3 :config="chartData.lineBar3Config" />
          </div>
        </div>
      </div>
    </div>
    <!-- 内容展示 -->
    <!-- 弹框 -->
    <el-dialog v-model="dialogVisible" title="实时物流数据详情" width="600" class="blue-dialog">
      <ul class="visibleBox">
        <li>
          <h2>路线</h2>
          <h3>{{ visibleNr.line }}</h3>
        </li>
        <li>
          <h2>车牌</h2>
          <h3>{{ visibleNr.car_number }}</h3>
        </li>
        <li>
          <h2>运单号</h2>
          <h3>{{ visibleNr.order_number }}</h3>
        </li>
      </ul>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" class="sureBtn">确定</el-button>
          <el-button type="primary" @click="dialogVisible = false" class="resetBtn">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹框 -->
    <!-- 弹框 -->
    <el-dialog v-model="dialogVisiblex" title="产业主体销售额详情" width="600" class="blue-dialog">
      <ul class="visibleBox">
        <li>
          <h2>产业主体</h2>
          <h3>{{ visibleNrx.name }}</h3>
        </li>
        <li>
          <h2>交易金额</h2>
          <h3>{{ visibleNrx.total_amount }}</h3>
        </li>
        <li>
          <h2>交易笔数</h2>
          <h3>{{ visibleNrx.total_count }}万元</h3>
        </li>
      </ul>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisiblex = false" class="sureBtn">确定</el-button>
          <el-button type="primary" @click="dialogVisiblex = false" class="resetBtn">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹框 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ScrennHeader from '@/components/ScrennHeader';
import AutoScrollTable from '@/components/AutoScrollTable';
import SortTable from '@/components/SortTable';
import LineBar from './components/LineBar.vue';
import LineBar2 from './components/LineBar2.vue';
import LineBar3 from './components/LineBar3.vue';
import LineChart from './components/LineChart.vue';
import RoseChart from './components/RoseChart.vue';
import statisticsChart2 from './components/statisticsChar2.vue';
import statisticsChart from './components/statisticsChart.vue';
import ProgressChart from './components/ProgressChart.vue';
import ProgressChart2 from './components/ProgressChart2.vue';
import Swiper from './components/Swiper.vue';
import ChatPanel from '../../components/ChatPanel/index.vue';
import {
  getGinsengPriceTrend,
  getGinsengSalesRanking,
  getAnnualStatistics,
  getGinsengTransactionAmount,
  getTransactionGrowthTrend,
  getIndustrySalesRanking,
  getSalesChannelRatio,
  getExportScaleAndGrowth,
  getRealTimeLogistics,
  getTourismFlowAndRevenue,
  getProductList
} from '@/api/screen/index6';
//tab
const tabs = ref(['原生野山参', '园参']);
const activeIndex = ref(0);
// 定义排名数据（格式必须为：[{ name: '名称', value: 数值 }, ...]）
//数据
const dataList01 = ref([]);
const dataList02 = ref([
  { index: 1, name: '产区作业1', type: '2025/05/12', content: '3小时' },
  { index: 2, name: '产区作业2', type: '2025/05/12', content: '3小时' },
  { index: 3, name: '产区作业3', type: '2025/05/12', content: '3小时' },
  { index: 4, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 5, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 6, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 7, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 8, name: '产区作业', type: '2025/05/12', content: '3小时' }
]);
const Topdata = ref({
  tradeAmount: 0,
  tradeQuantity: 0,
  exportAmount: 0,
  exportCount: 0
});
// 弹框样式
const dialogVisible = ref(false);
const dialogVisiblex = ref(false);
//弹框内容
const visibleNr = ref({});
const visibleNrx = ref({});

// 添加表格高度响应式变量
const tableHeight = ref('100%');

// 监听表格单元格点击（Element Plus 原生事件）
const handleRowClick = (row) => {
  dialogVisible.value = true;
  visibleNr.value = row;
};
const handleRowClick02 = (row) => {
  dialogVisiblex.value = true;
  visibleNrx.value = row;
};

// 各个图表的数据
const chartData = reactive({
  lineChartConfig: {
    data: {
      type: '0',
      price1List: [],
      price2List: []
    }
  },
  progressChartConfig1: {
    data: []
  },
  progressChartConfig2: {
    data: []
  },
  roseChartConfig: {
    data: []
  },
  lineBar2Config: {
    data: []
  },
  lineBarConfig: {
    data: []
  },
  statisticsChartConfig: {
    data: []
  },
  statisticsChartConfig2: {
    data: []
  },
  lineBar3Config: {
    data: []
  },
  swiperConfig: {
    data: []
  }
});
const xsjy = ref(0);
const xxjy = ref(0);
// ChatPanel 配置
const chatConfig = ref({
  ip: '************',
  title: '参管家',
  wsPort: 8282,
  httpPort: 8383,
  showTriggerButton: false
});

// 获取组件引用
const chatPanel = ref(null);

// 打开面板方法
const openChatPanel = () => {
  chatPanel.value?.openPanel();
};

const getData = async () => {
  const res = await getGinsengPriceTrend();
  chartData.lineChartConfig.data.price1List = res.data.price1List;
  chartData.lineChartConfig.data.dateList = res.data.dateList;

  const res2 = await getGinsengSalesRanking();
  chartData.progressChartConfig1.data = res2.data.innerList.map((item) => ({
    name: item.name,
    value: item.amount
  }));
  chartData.progressChartConfig2.data = res2.data.outerList.map((item) => ({
    name: item.name,
    value: item.amount
  }));
  const res3 = await getAnnualStatistics();
  console.log('%c [ res3 ]-439', 'font-size:13px; background:pink; color:#bf2c9f;', res3);
  Topdata.value = res3.data;

  const res4 = await getGinsengTransactionAmount();
  chartData.roseChartConfig.data = res4.data.map((item) => ({
    name: item.name,
    value: item.amount
  }));

  const res5 = await getIndustrySalesRanking();
  tableHeight.value = '99%';
  dataList01.value = res5.data.map((item, index) => ({
    index: index + 1,
    name: item.name,
    total_amount: item.total_amount,
    total_count: item.total_count
  }));
  setTimeout(() => {
    tableHeight.value = '100%';
  }, 300);

  const res6 = await getExportScaleAndGrowth();
  chartData.lineBarConfig.data = res6.data;

  const res7 = await getTransactionGrowthTrend();
  chartData.lineBar2Config.data = res7.data;

  const res8 = await getSalesChannelRatio();
  chartData.statisticsChartConfig.data = res8.data;
  chartData.statisticsChartConfig2.data = res8.data;
  xsjy.value = res8.data.amountList[0];
  xxjy.value = res8.data.amountList[1];

  const res9 = await getRealTimeLogistics();
  dataList02.value = res9.data;

  const res10 = await getTourismFlowAndRevenue();
  chartData.lineBar3Config.data = res10.data;

  const res11 = await getProductList();
  chartData.swiperConfig.data = res11.data;
  console.log(chartData.swiperConfig.data, '00000');
};

const clickTab = async (index) => {
  activeIndex.value = index;
  const res = await getGinsengPriceTrend();
  if (index === 0) {
    chartData.lineChartConfig.data.type = '0';
    chartData.lineChartConfig.data.price1List = res.data.price1List;
    chartData.lineChartConfig.data.price2List = [];
  } else {
    chartData.lineChartConfig.data.type = '1';
    chartData.lineChartConfig.data.price1List = [];
    chartData.lineChartConfig.data.price2List = res.data.price2List;
  }
};

onMounted(() => {
  getData();
});
</script>
<style scoped lang="scss">
.historyList {
  display: flex;
  justify-content: space-between;

  li {
    width: 50%;
    text-align: center;
    margin-top: 2%;

    h2 {
      font-size: 20px;
      color: #fff;
      margin: 0;
      font-weight: normal;
      margin-bottom: 5px;
    }

    h3 {
      font-size: 16px;
      color: #fff;
      margin: 0;
      font-weight: normal;
    }
  }
}

.fiveBox {
  width: 100%;
  height: 100%;

  .fiveTop {
    width: 100%;
    height: 49%;

    p {
      font-size: 14px;
      color: #74d5ff;
      margin-left: 10px;
    }
  }

  .fiveBottom {
    width: 100%;
    height: 49%;
    margin-top: 2%;

    p {
      font-size: 14px;
      color: #74d5ff;
      margin-left: 10px;
    }
  }
}
</style>
