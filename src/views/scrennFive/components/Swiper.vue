<template>
  <div class="swiper-box">
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <!-- 修复key绑定错误：应该是item.id而不是id -->
        <div class="swiper-slide" v-for="(item, index) in dataList" :key="item.id">
          <div class="fiveSwiperTop" style="height: 64%; margin-bottom: 3%">
            <div class="imgbox">
              <img :src="item.picture" :alt="item.productName" />
            </div>
          </div>
          <div class="fiveSwiperBottom" style="height: 33%">
            <div class="hjsjTop">
              <h3>{{ item.productName }}</h3>
                        <ul class="iconLeft iconSwiper">
                <li>
                  <h2>原料：</h2>
                  <p>{{ item.origin }}</p>
                </li>
                <li>
                  <h2>产地：</h2>
                  <p>{{ item.address }}</p>
                </li>
                <li>
                  <h2>企业：</h2>
                  <p>{{ item.enterpriseName }}</p>
                </li>
                <li>
                  <h2>酒精浓度：</h2>
                  <p>{{ item.alcohol }}度</p>
                </li>
                <li>
                  <h2>参龄：</h2>
                  <p>{{ item.level }}年</p>
                </li>
                <li>
                  <h2>保质期：</h2>
                  <p>{{ item.validTime }}年</p>
                </li>
                <li>
                  <h2>重金属：</h2>
                  <p>{{ item.metal }}</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Swiper from 'swiper';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import { onMounted, watch, ref } from 'vue';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});

// 恢复数据监听，确保能获取到props中的数据
const dataList = ref([]);

watch(
  () => props.config.data,
  (newVal) => {
    console.log('接收到的新数据:', newVal); // 新增日志，查看数据更新情况
    dataList.value = newVal;
    
    // 数据更新后重新初始化轮播（关键！）
    if (newVal.length > 0) {
      mySwiperFn();
    }
  },
  { deep: true, immediate: true } // 立即执行，捕获初始数据
);

const mySwiperFn = () => {
  // 先销毁可能存在的旧实例，避免重复初始化
  if (window.mySwiperInstance) {
    window.mySwiperInstance.destroy();
  }
  
  // 创建新实例并保存
  window.mySwiperInstance = new Swiper('.swiper-container', {
    loop: true,
    effect: 'fade',
    fadeEffect: {
      crossFade: true
    },
    speed: 1000,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
      pauseOnMouseEnter: true
    },
    resistanceRatio: 0,
    observer: true, // 监听DOM变化
    observeParents: true,
    modules: [Autoplay]
  });
};

onMounted(() => {
  console.log('初始props数据:', props.config.data);
  // 只有当数据存在时才初始化轮播
  if (dataList.value.length > 0) {
    mySwiperFn();
  }
});
</script>


<style scoped lang="scss">
.swiper-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .swiper-container {
    width: 100%;
    height: 100%;
    position: relative;

    .swiper-wrapper {
      display: flex;
      width: 100%;
      height: 100%;
      flex-shrink: 0;
      .swiper-slide {
        background: transparent;
        flex-shrink: 0;
        width: 100%;
        height: 100%;
        transition: opacity 1s ease;
        opacity: 0;
        position: relative;

        &.swiper-slide-active,
        &.swiper-slide-duplicate-active {
          opacity: 1;
        }
      }
    }
  }
}
.iconSwiper {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
  li {
    width: 50%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 1% 0;
    h2 {
      font-size: 12px;
      margin: 0;
      color: #74d5ff;
    }
    p {
      font-size: 12px;
      margin: 0;
      color: #74d5ff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.hjsjTop {
  width: 88%;
  margin: 3% auto 0 auto;
  padding-top: 6%;
  h3 {
    font-size: 20px;
    color: #ffec3d;
    margin-bottom: 2%;
  }
}
.fiveSwiperBottom {
  background: url('@/assets/images/u303.png');
  background-size: 100% 100%;

}
.fiveSwiperTop {
  background: url('@/assets/images/2.png');
  background-size: 100% 100%;
}
.imgbox {
  width:43%;
    height: 46%;
  position: absolute;
  left: 28.5%;
  top: 8%;
  border:1px solid #14a2df;
  img{
    width: 100%;
  }
}
</style>
