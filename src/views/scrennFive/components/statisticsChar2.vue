<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import 'echarts-liquidfill';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    num.value = newVal.rateList[1];
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
const num = ref(0);

// 生成图表配置项（黄色调修改）
const initChart = () => {
  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);
  var values = num.value / 100;
  var option = {
    title: [
      // {
      //   text: '本年收缴率',
      //   x: '22%',
      //   y: '70%',
      //   textStyle: {
      //     fontSize: 14,
      //     fontWeight: '100',
      //     color: '#5dc3ea',
      //     lineHeight: 16,
      //     textAlign: 'center'
      //   }
      // }
    ],
    series: [
      {
        type: 'liquidFill',
        radius: '90%',
        center: ['50%', '50%'],
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#06c461'
              },
              {
                offset: 1,
                color: '#00ddad'
              }
            ],
            globalCoord: false
          }
        ],
        data: [values], // data个数代表波浪数
        backgroundStyle: {
          borderWidth: 1,
          color: '#0c375f'
        },
        label: {
          normal: {
            textStyle: {
              fontSize: 20,
              color: '#fff'
            }
          }
        },
        outline: {
          // show: false
          borderDistance: 0,
          itemStyle: {
            borderWidth: 2,
            borderColor: '#19d9fe'
          }
        }
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
};
// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    window.removeEventListener('resize', handleResize);
  });
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 90px;
}
</style>
