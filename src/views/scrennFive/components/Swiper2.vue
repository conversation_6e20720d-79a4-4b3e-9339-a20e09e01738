<template>
  <div class="swiper-box">
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <div class="imgbox">
            <img src="@/assets/images/u273.svg">
          </div>
        </div>
        <div class="swiper-slide">
          <div class="imgbox">
            <img src="@/assets/images/u273.svg">
          </div>
        </div>
        <div class="swiper-slide">
          <div class="imgbox">
            <img src="@/assets/images/u273.svg">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Swiper from "swiper"
import { Autoplay } from "swiper/modules"
import "swiper/css"
import { onMounted } from "vue"

const mySwiperFn = () => {
  new Swiper(".swiper-container", {
    loop: true,
    effect: 'fade',
    fadeEffect: {
      crossFade: true
    },
    speed: 1000,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
      pauseOnMouseEnter: true
    },
    resistanceRatio: 0,
    observer: true,
    observeParents: true,
    modules: [Autoplay]
  })
}

onMounted(() => {
  mySwiperFn()
})
</script>

<style scoped lang="scss">
.swiper-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .swiper-container {
    width: 100%;
    height: 100%;
    position: relative;

    .swiper-wrapper {
      display: flex;
      width: 100%;
      height: 100%;
      flex-shrink: 0;

      .swiper-slide {
        background: transparent;
        flex-shrink: 0;
        width: 100%;
        height: 100%;
        transition: opacity 1s ease;
        opacity: 0;
        position: relative;

        &.swiper-slide-active,
        &.swiper-slide-duplicate-active {
          opacity: 1;
        }
      }
    }
  }
}
.imgbox{
  position: absolute;
  left: 32%;
  top: 10%;
}
</style>
