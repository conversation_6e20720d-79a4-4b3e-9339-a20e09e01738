<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  let option = {
    backgroundColor: '#011a38',

    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '1%',
      top: '15%',
      containLabel: true
    },
    legend: {
      data: ['营收额', '客流量'],
      top: '3%',
      right: 'center',
      textStyle: {
        color: '#74D5FF'
      },
      itemWidth: 12,
      itemHeight: 12
    },
    xAxis: {
      type: 'category',
      data: props.config.data.map((item) => item.month),
      axisLabel: {
        color: '#74D5FF',
        fontSize: 10,
        interval: 0
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '单位:万元',
        min: 0,
        max: function (value) {
          const maxAmount = Math.max(...props.config.data.map((item) => item.total_amount));
          return Math.ceil(maxAmount / 1000) * 1000;
        },
        axisLabel: {
          formatter: function (value) {
            return value.toFixed(1);
          },
          color: '#74D5FF',
          fontSize: 12
        },
        nameTextStyle: {
          fontSize: 12,
          color: '#74D5FF'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(66, 192, 255, .3)'
          }
        }
      },
      {
        type: 'value',
        name: '单位:人次',
        min: 0,
        max: function (value) {
          const maxCount = Math.max(...props.config.data.map((item) => item.total_count));
          return Math.ceil(maxCount / 10) * 10;
        },
        axisLabel: {
          formatter: '{value}',
          color: '#74D5FF',
          fontSize: 12
        },
        nameTextStyle: {
          fontSize: 12,
          color: '#74D5FF'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '营收额',
        type: 'bar',
        barWidth: 14,
        z: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value.toFixed(1) + ' 万元';
          }
        },
        itemStyle: {
          normal: {
            show: true,
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#015CC7'
                },
                {
                  offset: 1,
                  color: '#031E3E'
                }
              ],
              false
            ),
            barBorderRadius: 0,
            label: {
              show: false,
              position: 'top',
              color: '#56C1F8',
              formatter: function (p) {
                return p.value > 0 ? p.value : '';
              }
            }
          }
        },
        data: props.config.data.map((item) => item.total_amount)
      },
      {
        name: '客流量',
        type: 'line',
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          normal: {
            color: '#1BC7FF'
          }
        },

        itemStyle: {
          color: '#1BC7FF',
          borderColor: 'rgba(27, 199, 255, 0.30)',
          borderWidth: 6,
          shadowColor: 'rgba(27, 199, 255, 0.5)',
          shadowBlur: 6,
          opacity: 1
        },
        label: {
          show: false
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + ' 笔';
          }
        },
        data: props.config.data.map((item) => item.total_count),
        z: 2,
        smooth: true
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 9000);
};

onMounted(() => {
  nextTick(() => {
    initChart(); // 首次初始化
    resizeChart();

    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时器
  });
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
