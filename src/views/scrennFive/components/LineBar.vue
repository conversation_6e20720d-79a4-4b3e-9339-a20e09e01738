<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '25%',
      containLabel: true
    },
    legend: {
      data: ['交易额', '同比'],
      top: '3%',
      right: 'center',
      textStyle: {
        color: '#74D5FF'
      },
      itemWidth: 12,
      itemHeight: 12
    },
    xAxis: {
      type: 'category',
      data: props.config.data.map((item) => item.month),
      axisLabel: {
        color: '#74D5FF',
        fontSize: 10,
        interval: 0
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '单位:万元',
        // min: 0,
        // max: 10000,
        // interval: 2500,
        axisLabel: {
          formatter: '{value} 万元'
        },
        nameTextStyle: {
          fontSize: 12,
          color: '#74D5FF'
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            color: 'rgba(66, 192, 255, .3)'
          }
        },
        axisLabel: {
          color: '#74D5FF',
          fontSize: 12
        }
      },
      {
        type: 'value',
        name: '单位:%',
        // min: 100,
        max: 100,
        axisLabel: {
          formatter: '{value} %'
        },
        nameTextStyle: {
          fontSize: 12,
          color: '#74D5FF'
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            color: 'rgba(43, 137, 243, 0.16)'
          }
        },
        axisLabel: {
          color: '#74D5FF',
          fontSize: 12
        }
      }
    ],
    series: [
      {
        name: '交易额',
        type: 'bar',
        barWidth: 14,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' 万元';
          }
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#19d9fe'
              },
              {
                offset: 1,
                color: '#19d9fe'
              }
            ])
          }
        },
        data: props.config.data.map((item) => item.amount),
        smooth: 0.5,
        symbol: 'circle'
      },
      {
        name: '同比',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + '%';
          }
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            {
              offset: 0,
              color: '#e9cc10'
            },
            {
              offset: 1,
              color: '#e9cc10'
            }
          ])
        },
        data: props.config.data.map((item) => item.growthRate),
        smooth: 0.5,
        symbol: 'circle'
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 3000);
};

onMounted(() => {
  resizeChart();
  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
