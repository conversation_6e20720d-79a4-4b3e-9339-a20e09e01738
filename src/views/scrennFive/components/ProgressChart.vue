<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  let barWidth = 10 /* 进度条及进度条radius宽度 */,
    nameWidth = 60 /* 进度条名称宽度 */,
    unit = '万元' /* 单位 */,
    attaData = [] /* 进度条数据 */,
    attaVal = [] /* 进度条数值 */,
    topName = [] /* 进度条名称 */,
    salvProMax = []; /* 背景条数据 */
  let datas = props.config.data;
  let attackSourcesColor = [
    new echarts.graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: '#FE9C5A' },
      { offset: 1, color: '#EB3B5A' }
    ]),
    new echarts.graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: '#FFD14C' },
      { offset: 1, color: '#FA8231' }
    ]),
    new echarts.graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: '#FFEE96' },
      { offset: 1, color: '#F7B731' }
    ]),
    new echarts.graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: '#2EC7CF' },
      { offset: 1, color: '#395CFE' }
    ])
  ];
  datas.forEach((it, i) => {
    let itemStyle = {
      color: i > 3 ? attackSourcesColor[3] : attackSourcesColor[i]
    };
    topName[i] = `${it.name}`;
    attaVal[i] = it.value;
    attaData[i] = {
      value: parseFloat(it.value).toFixed(2),
      itemStyle: itemStyle
    };
  });
  /* 该值无具体作用，取进度最大值 * 1.2 */
  salvProMax = Array(attaVal.length).fill(Math.max(...attaVal) * 1.2);
  const option = {
    tooltip: {
      show: false,
      backgroundColor: 'rgba(3,169,244, 0.5)', //背景颜色（此时为默认色）
      textStyle: {
        fontSize: 16
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '5%',
      bottom: '5%',
      containLabel: true
    },
    legend: {
      show: false
    },
    xAxis: {
      show: false
    },
    yAxis: [
      {
        //名称
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        axisLabel: {
          textStyle: {
            color: '#74D5FF'
          },
          formatter: (val, i) => {
            return `{num|${(i += 1)}}{name|${val}}`;
          },
          rich: {
            num: {
              width: 16,
              fontSize: 14
            },
            name: {
              width: nameWidth,
              fontSize: 14
            }
          }
        },
        data: topName
      },
      {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        axisLabel: {
          textStyle: {
            color: '#88a3e6',
            fontSize: 20
          },
          formatter: (val) => {
            return `{num|${val}}{unit|(${unit})}`;
          },
          rich: {
            num: {
              fontSize: 14
            },
            unit: {
              fontSize: 14
            }
          }
        },
        data: attaVal
      }
    ],
    series: [
      // 进度条
      {
        zlevel: 1,
        name: '',
        type: 'bar',
        barWidth: barWidth,
        animationDuration: 1500,
        data: attaData,
        align: 'center',
        itemStyle: {
          normal: {
            barBorderRadius: barWidth
          }
        },
        label: {
          show: false
        }
      },
      // 背景条
      {
        name: '',
        type: 'bar',
        barWidth: barWidth,
        barGap: '-100%',
        data: salvProMax,
        itemStyle: {
          normal: {
            barBorderRadius: barWidth,
            color: 'rgba(165, 213, 232, 1)'
          },
          emphasis: {
            color: 'rgba(165, 213, 232, 1)'
          }
        }
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 10000);
};

onMounted(() => {
  nextTick(() => {
    initChart(); // 首次初始化
    resizeChart();

    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时器
  });
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
