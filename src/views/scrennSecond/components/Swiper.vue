<template>
  <div class="swiper-box">
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide" v-for="(item, index) in config.data" :key="index">
          <div class="hjsjTop">
            <h2>{{ item.base_name }}</h2>
            <p>{{ moment(currentTime).format('YYYY/MM/DD HH:mm:ss') }}</p>
          </div>
          <ul class="iconLeft iconSwiper">
            <li>
              <div class="text">
                <h2>{{ item.kqwd }}℃</h2>
                <p>空气温度</p>
              </div>
              <img src="@/assets/images/yuanquan.png" class="square" />
            </li>
            <li>
              <div class="text">
                <h2>{{ item.kqsd }}%</h2>
                <p>空气湿度</p>
              </div>
              <img src="@/assets/images/yuanquan.png" class="square" />
            </li>
            <li>
              <div class="text">
                <h2>{{ item.trsd }}℃</h2>
                <p>土壤温度</p>
              </div>
              <img src="@/assets/images/yuanquan.png" class="square" />
            </li>
            <li>
              <div class="text">
                <h2>{{ item.trwd }}%</h2>
                <p>土壤湿度</p>
              </div>
              <img src="@/assets/images/yuanquan.png" class="square" />
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Swiper from 'swiper';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import { onMounted, watch, ref, nextTick, onBeforeUnmount } from 'vue';
//引入moment
import moment from 'moment';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});

// 监听数据变化
watch(
  () => props.config.data,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      nextTick(() => {
        mySwiperFn();
      });
    }
  },
  { deep: true }
);

const currentTime = ref(Date.now());
const timer = ref(null);
const swiper = ref(null);

onMounted(() => {
  timer.value = setInterval(() => {
    currentTime.value = Date.now();
  }, 1000);
});
const mySwiperFn = () => {
  // 如果已存在swiper实例，先销毁
  if (swiper.value) {
    swiper.value.destroy();
  }

  // 创建新的swiper实例
  swiper.value = new Swiper('.swiper-container', {
    loop: true,
    effect: 'fade',
    fadeEffect: {
      crossFade: true
    },
    speed: 1000,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
      pauseOnMouseEnter: true
    },
    resistanceRatio: 0,
    observer: true,
    observeParents: true,
    modules: [Autoplay]
  });
};
onBeforeUnmount(() => {
  // 组件销毁时清理swiper实例
  if (swiper.value) {
    swiper.value.destroy();
    swiper.value = null;
  }
  clearInterval(timer.value);
});
</script>

<style scoped lang="scss">
.swiper-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .swiper-container {
    width: 100%;
    height: 100%;
    position: relative;

    .swiper-wrapper {
      display: flex;
      width: 100%;
      height: 100%;
      flex-shrink: 0;
      .swiper-slide {
        background: transparent;
        flex-shrink: 0;
        width: 100%;
        height: 100%;
        transition: opacity 1s ease;
        opacity: 0;
        position: relative;

        &.swiper-slide-active,
        &.swiper-slide-duplicate-active {
          opacity: 1;
        }
      }
    }
  }
}
.iconSwiper {
  display: flex;
  justify-content: space-between;
  li {
    width: 100px;
    position: relative;
    .text {
      width: 100%;
      position: absolute;
      left: 0;
      top: 28%;
      z-index: 9;
      text-align: center;
      h2 {
        margin: 0;
        color: #ffec3d;
        font-size: 16px;
      }
      p {
        color: #74d5ff;
      }
    }
    img {
      width: 100px;
      height: 100px;
    }
  }
}
.hjsjTop {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 5%;
  h2 {
    font-size: 18px;
    color: #74d5ff;
  }
  p {
    font-size: 14px;
    color: #ffec3d;
  }
}
</style>
