<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，修改为柱状图
  const option = {
    color: ['#19d9fe', '#1978e6', '#20bc8e', '#e9cc10', '#a24747', '#6b47a2'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      axisPointer: { type: 'shadow' },
      formatter: (params) => {
        // 温度℃ 湿度% 光照度万LX
        let str = '';
        params.forEach((item, idx) => {
          let unit = '';
          if (item.seriesName === '有机物含量') unit = '%';
          else if (item.seriesName === 'PH值') unit = '';
          else if (item.seriesName === '电导率') unit = 'mS/cm';
          str += `${item.seriesName}: ${item.value}${unit}`;
          if (idx !== params.length - 1) str += '<br/>';
        });
        return str;
      }
    },
    legend: {
      data: ['有机物含量', 'PH值', '电导率'],
      textStyle: {
        color: '#74D5FF'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '1%', // 增加底部空间避免标签重叠
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.config.data.map((item) => item.time),
      axisLine: {
        lineStyle: { color: 'rgba(66, 192, 255, .3)' }
      },
      axisLabel: {
        color: '#05D5FF',
        textStyle: { fontSize: 12 },
        interval: 0, // 显示所有月份标签
        rotate: 0 // 如需优化布局可调整旋转角度（如30度）
      },
      axisTick: { show: false }
    },
    yAxis: [
      {
        axisLabel: {
          formatter: '{value}',
          color: '#74D5FF',
          textStyle: { fontSize: 12 }
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: { color: 'rgba(66, 192, 255, .3)' }
        },
        splitLine: {
          lineStyle: { color: 'rgba(66, 192, 255, .3)' }
        }
      }
    ],
    series: [
      {
        name: '有机物含量',
        type: 'bar', // 修改为柱状图
      //   label: {
      //   show: true, // 开启显示数值标签
      //   position: 'top', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
      //   textStyle: {
      //     color: 'rgba(25, 217, 254, 0.9)', // 标签文字颜色，可按需调整
      //     fontSize: 12 // 标签文字大小，可按需调整
      //   }
      // },
        data: props.config.data.map((item) => item.yjwhl),
        barWidth: 6, // 柱状图宽度
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            1,
            0,
            0, // 纵向渐变（从下到上）
            [
              { offset: 0, color: 'rgba(25, 217, 254, 0.9)' },
              { offset: 1, color: 'rgba(25, 217, 254, 0.4)' }
            ]
          ),
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 5,
          shadowOffsetY: 2
        }
      },
      {
        name: 'PH值',
        type: 'bar',
//         label: {
//   show: true, // 开启显示数值标签
//   position: 'top', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
//   textStyle: {
//     color: 'rgba(25, 120, 230, 0.9)', // 标签文字颜色，可按需调整
//     fontSize: 12 // 标签文字大小，可按需调整
//   }
// },
        data: props.config.data.map((item) => item.ph),
        barWidth: 6,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(25, 120, 230, 0.9)' },
            { offset: 1, color: 'rgba(25, 120, 230, 0.4)' }
          ]),
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 5,
          shadowOffsetY: 2
        }
      },
      {
        name: '电导率',
        type: 'bar',
//         label: {
//   show: true, // 开启显示数值标签
//   position: 'top', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
//   textStyle: {
//     color: 'rgba(32, 188, 142, 0.9)', // 标签文字颜色，可按需调整
//     fontSize: 12 // 标签文字大小，可按需调整
//   }
// },
        data: props.config.data.map((item) => item.ddl),
        barWidth: 6,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(32, 188, 142, 0.9)' },
            { offset: 1, color: 'rgba(32, 188, 142, 0.4)' }
          ]),
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 5,
          shadowOffsetY: 2
        }
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 10000);
};

onMounted(() => {
  resizeChart();

  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
