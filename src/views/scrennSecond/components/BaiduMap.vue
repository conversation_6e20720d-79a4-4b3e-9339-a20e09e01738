<template>
  <div class="mapBox">
    <div id="allMap" ref="mapContainer" :class="containerClass"></div>
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, shallowRef, nextTick } from 'vue';

const props = defineProps({
  // 百度地图API密钥
  ak: {
    type: String,
    required: true
  },
  // 初始中心点坐标
  center: {
    type: Object,
    default: () => ({ lng: 126.965607, lat: 44.406106}),
    validator: (val) => 'lng' in val && 'lat' in val
  },
  // 初始缩放级别
  zoom: {
    type: Number,
    default: 15
  },
  // 地图容器样式类
  containerClass: {
    type: String,
    default: 'map-container'
  },
  // 是否启用滚轮缩放
  scrollWheelZoom: {
    type: Boolean,
    default: true
  },
  // 地图类型
  mapType: {
    type: String,
    default: 'normal',
    validator: (val) => ['normal', 'satellite', 'hybrid'].includes(val)
  },
  // 自定义控件
  controls: {
    type: Array,
    default: () => ['navigation', 'scale', 'overview', 'mapType']
  }
});

const emits = defineEmits([
  'ready',     // 地图初始化完成
  'click',     // 地图点击事件
  'moveend',   // 地图移动结束
  'zoomend',   // 地图缩放结束
  'markerclick' // 标记点击事件
]);

const mapContainer = ref(null);
const mapInstance = shallowRef(null);
const markers = shallowRef([]);
let BMapGL;
let map;
// 初始化百度地图
window.initMap = () => {
  if (!mapContainer.value) return;
  console.log("window",window)
  // 创建地图实例
  BMapGL = window.BMapGL;
  console.log("BMapGL", BMapGL)
  map = new BMapGL.Map("allMap", {
    minZoom: 5,
    maxZoom: 10,
    showVectorStreetLayer:false,
    showVectorLine:false
  });
  map.centerAndZoom(new BMapGL.Point(126.965607, 44.406106), 14);
  // 设置地图类型
  map.enableScrollWheelZoom();
  map.setMapType(BMAP_SATELLITE_MAP);
  // setMapType(map, props.mapType);

  // 添加地图控件
  addControls(map, props.controls);

  // 启用滚轮放大缩小
  if (props.scrollWheelZoom) {
    map.enableScrollWheelZoom();
  }

  // 绑定地图事件
  bindMapEvents(map);

  mapInstance.value = map;
  emits('ready', map);
};

// 设置地图类型
const setMapType = (map, type) => {
  const mapTypes = {
    'normal': BMAP_NORMAL_MAP,
    'satellite': BMAP_SATELLITE_MAP,
    'hybrid': BMAP_HYBRID_MAP
  };

  if (mapTypes[type]) {
    map.setMapType(mapTypes[type]);
  }
};

// 添加地图控件
const addControls = (map, controls) => {
  const controlMap = {
    'navigation': new BMapGL.NavigationControl(),
    'scale': new BMapGL.ScaleControl(),
    'overview': new BMapGL.OverviewMapControl(),
    'mapType': new BMapGL.MapTypeControl()
  };

  controls.forEach(control => {
    if (controlMap[control]) {
      map.addControl(controlMap[control]);
    }
  });
};

// 绑定地图事件
const bindMapEvents = (map) => {
  map.addEventListener('click', (e) => emits('click', e));
  map.addEventListener('moveend', (e) => emits('moveend', e));
  map.addEventListener('zoomend', (e) => emits('zoomend', e));
};

// 添加标记点
const addMarker = (position, options = {}) => {
  if (!mapInstance.value) return;

  const point = new BMapGL.Point(position.lng, position.lat);
  const marker = new BMapGL.Marker(point);

  // 设置标记属性
  if (options.icon) {
    const icon = new BMapGL.Icon(options.icon, new BMap.Size(options.width || 32, options.height || 32));
    marker.setIcon(icon);
  }

  // 添加到地图
  mapInstance.value.addOverlay(marker);
  markers.value.push(marker);

  // 绑定标记点击事件
  if (options.infoWindow) {
    const infoWindow = new BMap.InfoWindow(options.infoWindow.content, {
      width: options.infoWindow.width || 200,
      height: options.infoWindow.height || 100,
      title: options.infoWindow.title || ''
    });

    marker.addEventListener('click', () => {
      mapInstance.value.openInfoWindow(infoWindow, point);
      emits('markerclick', { marker, position, options });
    });
  }

  return marker;
};

// 清除所有标记
const clearMarkers = () => {
  if (mapInstance.value) {
    markers.value.forEach(marker => {
      mapInstance.value.removeOverlay(marker);
    });
    markers.value = [];
  }
};

// 调整地图中心点
const setCenter = (position) => {
  if (mapInstance.value) {
    const point = new BMapGL.Point(position.lng, position.lat);
    mapInstance.value.setCenter(point);
  }
};

// 调整地图缩放级别
const setZoom = (zoom) => {
  if (mapInstance.value) {
    mapInstance.value.setZoom(zoom);
  }
};

// 响应式处理
watch(() => props.center, (newVal) => {
  if (mapInstance.value) {
    setCenter(newVal);
  }
});

watch(() => props.zoom, (newVal) => {
  if (mapInstance.value) {
    setZoom(newVal);
  }
});

onMounted(() => {
  const script = document.createElement('script');
  script.src = "//api.map.baidu.com/api?type=webgl&v=1.0&ak=ConqWMS6IAb8BtGBbviXKMDNNeOUPgNH&callback=initMap";
  document.body.appendChild(script);

});

onUnmounted(() => {
  // 清理地图资源
  if (mapInstance.value) {
    clearMarkers();
    mapInstance.value.clearOverlays();
    mapInstance.value = null;
  }
});

// 暴露公共方法
const mapMethods = {
  addMarker,
  clearMarkers,
  setCenter,
  setZoom
};

defineExpose(mapMethods);
</script>

<style scoped>
.mapBox{
  width: 100%;
  height: 100%;
}
.map-container {
  width: 100%;
  height:100%;
}
</style>
