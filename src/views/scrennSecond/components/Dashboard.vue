<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: {
        offlineNum: 0,
        onlineNum: 0,
        total: 0
      }
    })
  }
});

// onlineNum在线
// offlineNum离线
// total总数
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

// 计算在线百分比
const calculateOnlinePercentage = () => {
  const { onlineNum, offlineNum, total } = props.config;
  const totalCount = total || onlineNum + offlineNum;
  if (totalCount === 0) return 0;
  return parseFloat(((onlineNum / totalCount) * 100).toFixed(2));
};

// 计算圆上点坐标的工具函数
function getCirlPoint(x0, y0, r, angle) {
  const x1 = x0 + r * Math.cos((angle * Math.PI) / 180);
  const y1 = y0 + r * Math.sin((angle * Math.PI) / 180);
  return { x: x1, y: y1 };
}

// 生成图表配置项（调整位置，解决偏上问题）
const getOption = () => {
  const onlinePercentage = calculateOnlinePercentage();

  const option = {
    tooltip: {
      formatter: (params) => {
        const { onlineNum, offlineNum, total } = props.config;
        return `在线率: ${onlinePercentage}%\n在线: ${onlineNum}\n离线: ${offlineNum}\n总数: ${
          total || onlineNum + offlineNum
        }`;
      },
      triggerOn: 'mousemove',
      backgroundColor: '#ffffff',
      borderColor: 'rgba(0,246,255,1)',
      borderWidth: 0.5
    },
    angleAxis: {
      max: 0,
      clockwise: false,
      startAngle: -45,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    radiusAxis: {
      type: 'category',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    polar: {
      center: ['50%', '70%'], // 核心调整：y轴从60%下移到70%
      radius: '20%'
    },
    series: [
      // 由外往内第一层线色部分
      {
        name: 'ring5',
        type: 'custom',
        coordinateSystem: 'none',
        renderItem: (params, api) => ({
          type: 'arc',
          shape: {
            cx: api.getWidth() * 0.5,
            cy: api.getHeight() * 0.8, // 核心调整：y轴从0.7下移到0.8
            r: Math.min(api.getWidth(), api.getHeight()) / 2,
            startAngle: (180 * Math.PI) / 180,
            endAngle: (360 * Math.PI) / 180
          },
          style: {
            stroke: '#01B5DB',
            fill: 'transparent',
            lineWidth: 1
          },
          silent: true
        }),
        data: [0]
      },
      // 由外往内第二层线色部分
      {
        zlevel: 1,
        type: 'pie',
        radius: ['94%', '92%'],
        center: ['50%', '80%'], // 核心调整：y轴从70%下移到80%
        data: [
          {
            value: 180,
            itemStyle: { normal: { color: '#01B5DB' } }
          },
          {
            value: 180,
            itemStyle: { opacity: 0 }
          }
        ],
        tooltip: { show: false },
        startAngle: 180,
        endAngle: 0,
        hoverAnimation: false,
        label: { show: false },
        labelLine: { show: false }
      },
      // 由外往内第三层灰色部分
      {
        zlevel: 1,
        type: 'pie',
        radius: ['92%', '83%'],
        center: ['50%', '80%'], // 核心调整：y轴从70%下移到80%
        data: [
          {
            value: 180,
            itemStyle: { normal: { color: 'rgba(0,213,255, 0.2)' } }
          },
          {
            value: 188,
            itemStyle: { opacity: 0 }
          }
        ],
        tooltip: { show: false },
        startAngle: 178,
        endAngle: 2,
        hoverAnimation: false,
        label: { show: false },
        labelLine: { show: false }
      },
      // 由外往内第四层刻度部分
      {
        type: 'gauge',
        radius: '78%',
        center: ['50%', '80%'], // 核心调整：y轴从70%下移到80%
        splitNumber: 4,
        min: 0,
        max: 100,
        startAngle: 180,
        endAngle: 0,
        axisLine: {
          show: true,
          lineStyle: { width: 0, color: '#00c8f1' }
        },
        axisTick: {
          show: true,
          lineStyle: { color: '#00D5FF', width: 1 },
          length: 8,
          splitNumber: 10
        },
        splitLine: {
          show: true,
          length: 8,
          lineStyle: { fontWeight: 400, color: '#00D5FF' }
        },
        axisLabel: { show: false },
        pointer: { show: 0 },
        detail: { show: 0 }
      },
      // 最里面黄色圆环进度部分
      {
        zlevel: 2,
        type: 'pie',
        radius: ['55%', '62%'],
        center: ['50%', '80%'],
        data: [
          {
            value: onlinePercentage,
            name: '在线率',
            itemStyle: {
              normal: {
                color: '#FFCF40',
                borderWidth: 2,
                shadowBlur: 3,
                shadowColor: '#FFCF40'
              }
            }
          },
          {
            value: 100 - onlinePercentage,
            itemStyle: { opacity: 0 }
          }
        ],
        tooltip: { show: true },
        startAngle: 180,
        endAngle: 0,
        hoverAnimation: false,
        label: { show: false },
        labelLine: { show: false }
      },
      // 最里面圆环底色部分
      {
        zlevel: 1,
        type: 'pie',
        radius: ['55%', '62%'],
        center: ['50%', '80%'], // 核心调整：y轴从70%下移到80%
        data: [
          {
            value: 180,
            itemStyle: {
              normal: {
                color: '#1E3756',
                borderWidth: 2,
                borderColor: '#1E3756'
              }
            }
          },
          {
            value: 180,
            itemStyle: { opacity: 0 }
          }
        ],
        tooltip: { show: false },
        startAngle: 180,
        endAngle: 0,
        hoverAnimation: false,
        label: { show: false },
        labelLine: { show: false }
      },
      // 标题和数字部分
      {
        type: 'gauge',
        radius: '45%',
        center: ['50%', '70%'], // 核心调整：y轴从60%下移到70%
        min: 0,
        max: 100,
        splitNumber: 5,
        axisLine: { show: false, lineStyle: { opacity: 0 } },
        axisLabel: { show: false },
        pointer: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        detail: {
          formatter: '{value}%',
          color: '#fff',
          fontSize: 22,
          offsetCenter: [0, 15],
          fontWeight: 700
        },
        title: { show: false },
        data: [{ value: onlinePercentage }]
      },
      // 右侧绿点1
      {
        name: 'ring5',
        type: 'custom',
        coordinateSystem: 'none',
        zIndex: 9,
        z: 9,
        renderItem: (params, api) => {
          const x0 = api.getWidth() * 0.502;
          const y0 = api.getHeight() * 0.8; // 核心调整：y轴从0.7下移到0.8
          const r = Math.min(api.getWidth(), api.getHeight()) / 2;
          const point = getCirlPoint(x0, y0, r, 360);
          return {
            type: 'circle',
            shape: { cx: point.x, cy: point.y, r: 2 },
            style: { stroke: '#01B5DB', fill: '#01B5DB' },
            silent: true
          };
        },
        data: [0]
      },
      // 右侧绿点2
      {
        name: 'ring5',
        type: 'custom',
        coordinateSystem: 'none',
        zIndex: 9,
        z: 9,
        renderItem: (params, api) => {
          const x0 = api.getWidth() * 0.502;
          const y0 = api.getHeight() * 0.8; // 核心调整：y轴从0.7下移到0.8
          const r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.93;
          const point = getCirlPoint(x0, y0, r, 360);
          return {
            type: 'circle',
            shape: { cx: point.x, cy: point.y, r: 4 },
            style: { stroke: '#01B5DB', fill: '#01B5DB' },
            silent: true
          };
        },
        data: [0]
      },
      // 左侧绿点1
      {
        name: 'ring5',
        type: 'custom',
        coordinateSystem: 'none',
        zIndex: 9,
        z: 9,
        renderItem: (params, api) => {
          const x0 = api.getWidth() * 0.498;
          const y0 = api.getHeight() * 0.8; // 核心调整：y轴从0.7下移到0.8
          const r = Math.min(api.getWidth(), api.getHeight()) / 2;
          const point = getCirlPoint(x0, y0, r, 180);
          return {
            type: 'circle',
            shape: { cx: point.x, cy: point.y, r: 2 },
            style: { stroke: '#01B5DB', fill: '#01B5DB' },
            silent: true
          };
        },
        data: [0]
      },
      // 左侧绿点2
      {
        name: 'ring5',
        type: 'custom',
        coordinateSystem: 'none',
        zIndex: 9,
        z: 9,
        renderItem: (params, api) => {
          const x0 = api.getWidth() * 0.498;
          const y0 = api.getHeight() * 0.8; // 核心调整：y轴从0.7下移到0.8
          const r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.93;
          const point = getCirlPoint(x0, y0, r, 180);
          return {
            type: 'circle',
            shape: { cx: point.x, cy: point.y, r: 4 },
            style: { stroke: '#01B5DB', fill: '#01B5DB' },
            silent: true
          };
        },
        data: [0]
      }
    ]
  };

  // 设置进度相关数据
  const progressValue = (onlinePercentage / 100) * 180;
  option.series[4].data = [
    {
      value: progressValue,
      name: '在线率',
      itemStyle: {
        normal: {
          color: '#FFCF40',
          borderWidth: 2,
          shadowBlur: 3,
          shadowColor: '#FFCF40'
        }
      }
    },
    {
      value: 180 - progressValue,
      itemStyle: { opacity: 0 }
    }
  ];
  option.series[6].data = [{ value: onlinePercentage }];

  return option;
};

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  chartInstance = echarts.init(chartContainer.value);
  chartInstance.setOption(getOption());
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  reloadTimer = setInterval(initChart, 3000);
};

onMounted(() => {
  initChart();
  resizeChart();
  window.addEventListener('resize', handleResize);
  startReloadTimer();
});

onBeforeUnmount(() => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
