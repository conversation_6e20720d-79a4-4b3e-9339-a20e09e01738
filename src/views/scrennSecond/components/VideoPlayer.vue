<template>
  <div class="video-player" :class="{ 'has-overlay': !autoplaySuccess }">
    <!-- 视频元素 -->
    <video
      ref="videoRef"
      :src="source"
      :poster="poster"
      :autoplay="autoplay"
      :muted="muted"
      :loop="loop"
      playsinline
      preload="auto"
      disableRemotePlayback
      @loadeddata="onVideoLoaded"
      @error="onVideoError"
      @click="showDialog = true"
      style="cursor: pointer"
    ></video>

    <!-- 自动播放失败时显示的覆盖层 -->
    <div v-if="!autoplaySuccess && showOverlay" class="overlay" @click="playVideo">
      <div class="play-button">
        <i class="fa fa-play-circle"></i>
        <p v-if="overlayText" style="width: 100%;">{{ overlayText }}</p>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loader">
      <i class="fa fa-spinner fa-spin"></i>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      <i class="fa fa-exclamation-circle"></i>
      <p>{{ errorMessage }}</p>
    </div>

    <!-- 弹窗全屏播放 -->
 

    <!-- 弹框 -->
    <el-dialog v-model="showDialog" title="视频详情" width="1000" class="blue-dialog ">
      <video
        :src="source"
        :poster="poster"
        controls
        style="width: 100%; height: 500px; "
      ></video>
    </el-dialog>
     <!-- 弹框 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineProps, defineEmits } from 'vue';

// 定义组件 props
const props = defineProps({
  source: {
    type: String,
    required: true
  },
  poster: {
    type: String,
    default: ''
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  muted: {
    type: Boolean,
    default: true
  },
  loop: {
    type: Boolean,
    default: true
  },
  overlayText: {
    type: String,
    default: ''
  },
  showOverlay: {
    type: Boolean,
    default: true
  }
});

// 定义组件事件
const emits = defineEmits(['loaded', 'error', 'play', 'pause']);

// 组件状态
const videoRef = ref(null);
const isLoading = ref(true);
const autoplaySuccess = ref(false);
const errorMessage = ref('');
const showDialog = ref(false);

onMounted(async () => {
  if (!props.autoplay) return;

  try {
    // 尝试自动播放
    await videoRef.value.play();
    autoplaySuccess.value = true;
    emits('play');
  } catch (error) {
    console.log('自动播放失败，需要用户交互:', error);
    autoplaySuccess.value = false;
  } finally {
    isLoading.value = false;
  }

  // 监听用户滚动或点击，尝试再次触发自动播放
  const handleInteraction = async () => {
    if (!autoplaySuccess.value && props.autoplay) {
      try {
        await videoRef.value.play();
        autoplaySuccess.value = true;
        emits('play');
        // 移除事件监听，避免重复触发
        window.removeEventListener('scroll', handleInteraction);
        window.removeEventListener('click', handleInteraction);
      } catch (error) {
        console.log('用户交互后仍无法播放:');
      }
    }
  };

  // 添加事件监听
  window.addEventListener('scroll', handleInteraction);
  window.addEventListener('click', handleInteraction);

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('scroll', handleInteraction);
    window.removeEventListener('click', handleInteraction);
  });
});

// 手动播放视频方法
const playVideo = async () => {
  try {
    await videoRef.value.play();
    autoplaySuccess.value = true;
    emits('play');
  } catch (error) {
    console.error('手动播放失败:', error);
    errorMessage.value = '无法播放视频，请尝试刷新页面';
    emits('error', error);
  }
};

// 视频加载成功事件
const onVideoLoaded = () => {
  isLoading.value = false;
  emits('loaded');
};

// 视频加载错误事件
const onVideoError = (event) => {
  isLoading.value = false;
  const error = event.target.error;
  console.error('视频加载错误');
  errorMessage.value = `视频加载失败`;
  emits('error', error);
};
</script>

<style scoped>

:deep(.el-dialog) {
  background: #00192d !important;
}

.video-player {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #000;
}

video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
  z-index: 10;
}

.overlay:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.play-button {
  text-align: center;
  color: #74d5f3;
}

.play-button i {
  font-size: 6rem;
  margin-bottom: 1rem;
  transition: transform 0.3s;
}

.play-button:hover i {
  transform: scale(1.1);
}

.loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #74d5f3;
  font-size: 2rem;
  z-index: 5;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #74d5f3;
  background-color: rgba(0, 0, 0, 0.7);
  text-align: center;
  z-index: 5;
}
</style>
