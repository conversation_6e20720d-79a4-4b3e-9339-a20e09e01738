<template>
  <div class="page">
    <div class="warning-view">
      <div class="scroll-view" ref="scrollViewRef" @mouseenter="onMouseenter" @mouseleave="onMouseleave">
        <div ref="listRef" class="list" v-for="(p, n) in count" :key="n">
          <div class="item" v-for="(item, index) in datalist" :key="index">
            <div class="s_title">
              <div class="s_title_left">
                <img src="@/assets/images/s_ico.png" />
                <h2>{{ item.name }}</h2>
              </div>
              <div class="s_title_right">
                <span v-if="item.type == 1" style="color: #ce555e">入侵告警</span>
                <span v-else style="color: #ada5fb">闯入告警</span>
              </div>
            </div>
            <div class="titlebox">{{ item.content }}</div>
            <ul class="listNr">
              <li>负责人：{{ item.person }}</li>
              <li>告警时间：{{ item.time }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { time } from 'echarts';
import { ref, onBeforeMount, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
const datalist = ref([]); //列表数据
const listRef = ref(); //列表dom
const scrollViewRef = ref(); //滚动区域dom
const count = ref(1); //列表个数

let intervalId = null;
let isAutoScrolling = true; //是否自动滚动标识

// 监听配置变化
watch(
  () => props.config.data,
  (newData) => {
    if (newData) {
      datalist.value = newData;
      nextTick(() => {
        //判断列表是否生成滚动条
        count.value = hasScrollBar() ? 2 : 1;
        //有滚动条开始自动滚动
        if (count.value == 2) {
          // 清除之前的定时器
          if (intervalId) {
            clearInterval(intervalId);
          }
          autoScrolling();
        }
      });
    }
  },
  { immediate: true, deep: true }
);

onMounted(async () => {
  nextTick(() => {
    //判断列表是否生成滚动条
    count.value = hasScrollBar() ? 2 : 1;
    //有滚动条开始自动滚动
    if (count.value == 2) {
      autoScrolling();
    }
  });
});
//判断列表是否有滚动条
const hasScrollBar = () => {
  return scrollViewRef.value.scrollHeight > scrollViewRef.value.clientHeight;
};
//设置自动滚动
const autoScrolling = () => {
  intervalId = setInterval(() => {
    if (scrollViewRef.value.scrollTop < listRef.value[0].clientHeight) {
      scrollViewRef.value.scrollTop += isAutoScrolling ? 1 : 0;
    } else {
      scrollViewRef.value.scrollTop = 0;
    }
  }, 30);
};

onBeforeUnmount(() => {
  //离开页面清理定时器
  intervalId && clearInterval(intervalId);
});

//鼠标进入，停止滚动
const onMouseenter = () => {
  isAutoScrolling = false;
};
//鼠标移出，继续滚动
const onMouseleave = () => {
  isAutoScrolling = true;
};
</script>

<style scoped>
.page {
  height: 100%;
}
.warning-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.label {
  color: #fff;
  padding: 20px;
  font-size: 22px;
}
.scroll-view {
  flex: 1;
  height: 0;
  width: 100%;
  overflow-y: auto;
}
.list {
  width: 100%;
  box-sizing: border-box;
}
.item {
  width: 100%;
  height: 120px;
  min-height: 120px;
  background: url('@/assets/images/s_bg.png');
  background-size: 100% 100%;
  margin-bottom: 10px;
  cursor: pointer;
}
.s_title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.s_title::after {
  content: '';
  background: url('@/assets/images/s_line.png');
  height: 67px;
  width: 287px;
  position: absolute;
  bottom: -20px;
  left: -30px;
}
.s_title_left {
  height: 50px;
  line-height: 50px;
  display: flex;
  justify-content: flex-start;
  img {
    margin-right: 5px;
    width: 60px;
    height: 60px;
  }
  h2 {
    line-height: 50px;
    margin: 0;
    color: #74d5ff;
    font-size: 18px;
  }
}
.s_title_right {
  span {
    line-height: 50px;
    margin-right: 20px;
  }
}
.titlebox {
  font-size: 16px;
  color: #74d5ff;
  padding: 5px 20px 0 20px;
}
.listNr {
  padding: 5px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  li {
    width: 48%;
    color: #74d5ff;
    font-size: 12px;
  }
}
/*隐藏滚动条
 */
::-webkit-scrollbar {
  display: none;
}
</style>
