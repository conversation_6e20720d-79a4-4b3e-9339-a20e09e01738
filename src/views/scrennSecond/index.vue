<template>
  <div class="pictureImg">
    <!-- 按钮 -->
    <div class="floatingBtn float-animation" @click="openChatPanel">
      <img src="@/assets/images/Floating.gif" />
      <h2>参管家</h2>
    </div>
    <!-- 按钮 -->
    <!-- 聊天面板 -->
    <ChatPanel ref="chatPanel" :config="chatConfig" :showTriggerButton="false" />
    <!-- 标题 -->
    <ScrennHeader />
    <!-- 标题 -->
    <!-- 内容展示 -->
    <div class="viewport">
      <div class="columnLeft">
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>环境数据</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <Swiper :config="chartData.swiperConfig" />
          </div>
        </div>
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>视频监控</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <ul class="videoBox">
              <li style="margin-bottom: 2%">
                <VideoPlayer :autoplay="true" :loop="true" :muted="true" :source="videoSource01" />
              </li>
              <li style="margin-bottom: 2%">
                <VideoPlayer :autoplay="true" :loop="true" :muted="true" :source="videoSource04" />
              </li>
              <li>
                <VideoPlayer :autoplay="true" :loop="true" :muted="true" :source="videoSource03" />
              </li>
              <li>
                <!-- 使用自定义视频播放器组件 -->
                <VideoPlayer :autoplay="true" :loop="true" :muted="true" :source="videoSource02" />
              </li>
            </ul>
          </div>
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>智慧预测</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <auto-scroll-table :data="dataList01" @custom-event="handleRowClick">
              <el-table-column align="center" label="区域">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.baseName }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="预警类型">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.title }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="内容" prop="status">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.content }}</span>
                </template>
              </el-table-column>
            </auto-scroll-table>
          </div>
        </div>
      </div>
      <div class="columnCenter">
        <div class="columnCenterTop" style="height: 66.5%">
          <div class="scrennSecondStyle">
            <ul class="scrennSecondData">
              <li v-for="(item, index) in homeStatsConfig" :key="index">
                <div class="icoBox">
                  <div v-if="item.typeName == '监控'">
                    <img class="float-animation" src="@/assets/images/ico1.png" style="width: 80%" />
                    <img src="@/assets/images/u907.png" style="width: 100px; margin-top: -30px" />
                  </div>
                  <div v-if="item.typeName == '气象'">
                    <img class="float-animation" src="@/assets/images/ico2.png" style="width: 80%" />
                    <img src="@/assets/images/u1297.png" style="width: 50px; margin-top: -30px" />
                  </div>
                  <div v-if="item.typeName == '墒情'">
                    <img class="float-animation" src="@/assets/images/ico3.png" style="width: 80%" />
                    <img src="@/assets/images/u1301.png" style="width: 50px; margin-top: -30px" />
                  </div>
                  <div v-if="item.typeName == '虫情'">
                    <img class="float-animation" src="@/assets/images/ico4.png" style="width: 80%" />
                    <img src="@/assets/images/u924.png" style="width: 50px; margin-top: -30px" />
                  </div>
                  <div v-if="item.typeName == '围栏'">
                    <img class="float-animation" src="@/assets/images/ico5.png" style="width: 80%" />
                    <img src="@/assets/images/u1315.png" style="width: 50px; margin-top: -30px" />
                  </div>
                  <span>{{ item.typeName }}</span>
                </div>
                <div class="jirongText">
                  <p v-if="item.typeName != '围栏'">{{ item.deviceCount }}</p>
                  <!--         等于围栏的时候 把数量除以1000.toFixed(4)         -->
                  <p v-if="item.typeName == '围栏'">{{ (item.deviceCount / 1000).toFixed(2) }}</p>

                  <h2>
                    {{
                      item.typeName == '气象'
                        ? '套'
                        : item.typeName == '墒情'
                          ? '套'
                          : item.typeName == '虫情'
                            ? '台'
                            : item.typeName == '围栏'
                              ? '公里'
                              : item.typeName == '监控'
                                ? '台'
                                : ''
                    }}
                  </h2>
                </div>
              </li>
              <!-- <li>
                <div class="icoBox">
                  <img src="@/assets/images/ico2.png" class="float-animation" style="width: 40px; height: 40px" />
                  <img src="@/assets/images/u1297.png" style="width: 50px; margin-top: -30px" />
                  <span>气象</span>
                </div>
                <div class="jirongText">
                  <p>297</p>
                  <h2>套</h2>
                </div>
              </li> -->
              <!-- <li>
                <div class="icoBox">
                  <img src="@/assets/images/ico3.png" class="float-animation" style="width: 40px; height: 40px" />
                  <img src="@/assets/images/u1301.png" style="width: 50px; margin-top: -30px" />
                  <span>墒情</span>
                </div>
                <div class="jirongText">
                  <p>189</p>
                  <h2>套</h2>
                </div>
              </li> -->
              <!-- <li>
                <div class="icoBox">
                  <img src="@/assets/images/ico4.png" class="float-animation" style="width: 40px; height: 40px" />
                  <img src="@/assets/images/u924.png" style="width: 50px; margin-top: -30px" />
                  <span>虫情</span>
                </div>
                <div class="jirongText">
                  <p>1442</p>
                  <h2>台</h2>
                </div>
              </li> -->

              <!-- <li>
                <div class="icoBox">
                  <img src="@/assets/images/ico5.png" class="float-animation" style="width: 40px; height: 40px" />
                  <img src="@/assets/images/u1315.png" style="width: 50px; margin-top: -30px" />
                  <span>围栏</span>
                </div>
                <div class="jirongText">
                  <p>363</p>
                  <h2>公里</h2>
                </div>
              </li> -->
            </ul>
            <div class="scrennSecondEchart">
              <!-- 地图 -->
              <div id="container" class="map-container"></div>
            </div>
          </div>
        </div>
        <div class="columnCenterBottom">
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>气象数据走势图</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <LineChart :config="chartData.lineChartConfig" />
            </div>
          </div>
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>生态环境分析</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <BarList :config="chartData.barListConfig" />
            </div>
          </div>
        </div>
      </div>
      <div class="columnRight">
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>设备在线率统计</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <div class="sbzxBox">
              <div class="sbzxLeft">
                <div style="height: 140px">
                  <Dashboard :config="dashboardConfig" />
                </div>
                <p>设备在线率</p>
              </div>
              <div>
                <div class="sbzxCenter">
                  <p>物联网设备总数</p>
                  <span style="color: #ffec3d">{{ dashboardConfig.total }}</span>
                </div>
                <ul class="sbzxRight">
                  <li>
                    <p>在线数</p>
                    <span style="color: #00e676">{{ dashboardConfig.onlineNum }}</span>
                  </li>
                  <li>
                    <p>离线数</p>
                    <span style="color: #ff6b6b">{{ dashboardConfig.offlineNum }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>设备离线告警</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <auto-scroll-table :data="dataList02" :scroll-delay="30" @custom-event="handleRowClick02">
              <el-table-column align="center" label="设备名称">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.deviceName }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="告警时间">
                <template v-slot="scope">
                  <span class="customSpan">{{ formatTime(scope.row.time) }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="离线时长">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.alarmTime }}小时</span>
                </template>
              </el-table-column>
            </auto-scroll-table>
          </div>
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>异常预警</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <ScrollList :config="chartData.scrollListConfig" />
          </div>
        </div>
      </div>
    </div>
    <!-- 内容展示 -->
    <!-- 弹框 -->
    <el-dialog v-model="dialogVisible" class="blue-dialog" title="智慧预测详情" width="600">
      <ul class="visibleBox">
        <li>
          <h2>区域</h2>
          <h3>{{ visibleNr.baseName }}</h3>
        </li>
        <li>
          <h2>预警类型</h2>
          <h3>{{ visibleNr.title }}</h3>
        </li>
        <li>
          <h2>内容</h2>
          <h3>{{ visibleNr.content }}</h3>
        </li>
      </ul>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="sureBtn" @click="dialogVisible = false">确定</el-button>
          <el-button class="resetBtn" type="primary" @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹框 -->
    <!-- 弹框 -->
    <el-dialog v-model="dialogVisiblex" class="blue-dialog" title="设备离线告警详情" width="600">
      <ul class="visibleBox">
        <li>
          <h2>设备名称</h2>
          <h3>{{ visibleNrx.deviceName }}</h3>
        </li>
        <li>
          <h2>告警时间</h2>
          <h3>{{ formatTime(visibleNrx.time) }}</h3>
        </li>
        <li>
          <h2>离线时长</h2>
          <h3>{{ visibleNrx.alarmTime }}小时</h3>
        </li>
      </ul>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="sureBtn" @click="dialogVisiblex = false">确定</el-button>
          <el-button class="resetBtn" type="primary" @click="dialogVisiblex = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹框 -->
  </div>
  <!-- 自定义地图弹窗 -->
  <div v-if="showCustomInfoWindow" :style="customInfoWindowStyle" class="customInfoWindow">
    <div class="customInfoContent">
      <div class="customInfoHeader">
        <h3>{{ selectedPoint?.name }}</h3>
        <button class="closeBtn" @click="closeCustomInfoWindow">×</button>
      </div>
      <div class="customInfoBody">
        <hr />
        <div v-if="selectedPoint?.devList" class="devList">
          <div v-for="(item, index) in selectedPoint.devList" :key="index" class="devItem">
            <p>{{ item.mc }}: {{ item.sl }}</p>
          </div>
        </div>
        <div v-else class="noData">暂无设备数据</div>
      </div>
    </div>
  </div>
  <!-- 自定义地图弹窗2 -->
  <div v-if="showCustomInfoWindow2" :style="customInfoWindowStyle" class="customInfoWindow">
    <div class="customInfoContent">
      <div class="customInfoHeader">
        <h3>{{ selectedPoint2.device_name }}</h3>
        <button class="closeBtn" @click="closeCustomInfoWindow">×</button>
      </div>
      <div class="customInfoBody">
        <hr />
        <div class="devList">
          <div class="devItem">
            <p>设备类型: {{ selectedPoint2.device_detail_type }}</p>
            <p>设备编码: {{ selectedPoint2.device_code }}</p>
            <p>基地名称: {{ selectedPoint2.base_name }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, reactive } from 'vue';
import ScrennHeader from '@/components/ScrennHeader';
import LineChart from './components/LineChart.vue';
import Swiper from './components/Swiper.vue';
import AutoScrollTable from '@/components/AutoScrollTable';
import ScrollList from './components/ScrollList.vue';
import BarList from './components/BarList.vue';
import Dashboard from './components/Dashboard.vue';
import ChatPanel from '../../components/ChatPanel/index.vue';
import { parseTime } from '@/utils/ruoyi';

//视频播放组件
import VideoPlayer from './components/VideoPlayer.vue';
const videoSource01 = '/video/1.mp4';
const videoSource02 = '/video/2.mp4';
const videoSource03 = '/video/3.mp4';
const videoSource04 = '/video/4.mp4';
const videoPlayerRef = ref(null);
onMounted(() => {
  // 获取视频播放器组件引用
  videoPlayerRef.value = document.querySelector('video');
});

// getMapData;
import {
  getEnvironmentData,
  getWeatherTrend,
  getEnvironmentAnalysis,
  getDeviceOnlineStats,
  getDeviceOfflineAlarms,
  getAbnormalWarnings,
  getHomeStats,
  getWisdomPrediction,
  getMapData,
  getDeviceListByBaseId,
  getFenceList
} from '@/api/screen/index2';

// 添加表格高度响应式变量
const tableHeight = ref('100%');

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');
};
const showCustomInfoWindow2 = ref(false);
// 各个图表的数据
const chartData = reactive({
  swiperConfig: {
    data: []
  },
  lineChartConfig: {
    data: []
  },
  barListConfig: {
    data: []
  },
  scrollListConfig: {
    data: []
  }
});

// 弹框样式
const dialogVisible = ref(false);
const dialogVisiblex = ref(false);
//弹框内容
const visibleNr = ref({});
const visibleNrx = ref({});
//数据
const dataList01 = ref([]);
const dataList02 = ref([]);
const mapId = ref(null);
const shulanPoints3 = ref([]);
// 监听表格单元格点击（Element Plus 原生事件）
const handleRowClick = (row) => {
  dialogVisible.value = true;
  visibleNr.value = row;
};
const handleRowClick02 = (row) => {
  dialogVisiblex.value = true;
  visibleNrx.value = row;
};

const shulanPoints = ref([]);
// 自定义弹窗相关
const showCustomInfoWindow = ref(false);
const selectedPoint = ref(null);
const selectedPoint2 = ref(null);
const customInfoWindowStyle = ref({
  left: '0px',
  top: '0px'
});

// 生命周期钩子
onMounted(() => {});

// 百度地图 API Key
const apiKey = 'ConqWMS6IAb8BtGBbviXKMDNNeOUPgNH';

//地图

// 百度地图API加载状态
const mapLoaded = ref(false);
// 地图实例
let mapInstance = null;
// 边界多边形实例
const boundaryPolygon = ref(null);
// 坐标点覆盖物数组
const pointMarkers = ref([]);
// 设备点覆盖物数组
const deviceMarkers = ref([]);

// 设备类型与图标映射
const deviceTypeIconMap = {
  监控: { url: new URL('/src/assets/images/ico1.png', import.meta.url).href, width: 15, height: 15 },
  气象: { url: new URL('/src/assets/images/ico2.png', import.meta.url).href, width: 15, height: 15 },
  墒情: { url: new URL('/src/assets/images/ico3.png', import.meta.url).href, width: 15, height: 15 },
  虫情: { url: new URL('/src/assets/images/ico4.png', import.meta.url).href, width: 15, height: 15 },
  围栏: { url: new URL('/src/assets/images/ico5.png', import.meta.url).href, width: 15, height: 15 }
};

// 清除设备点
const clearDeviceMarkers = () => {
  if (!mapInstance) return;
  deviceMarkers.value.forEach((marker) => mapInstance.removeOverlay(marker));
  deviceMarkers.value = [];
};

// 监听地图缩放事件
const handleMapZoom = () => {
  // if (!mapInstance) return;
  // mapInstance.addEventListener('zoomend', async () => {
  //   const zoom = mapInstance.getZoom();
  //   if (zoom >= 12) {
  //     // 获取设备点
  //     const res = await getDeviceListByBaseId();
  //     if (res.code === 200) {
  //       addDeviceMarkers(res.data);
  //     }
  //   }
  //   if (zoom < 8) {
  //     clearDeviceMarkers();
  //   }
  // });
};

// 加载百度地图API
const loadBMapScript = () => {
  return new Promise((resolve, reject) => {
    if (window.BMapGL) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = '//api.map.baidu.com/api?type=webgl&v=1.0&ak=ConqWMS6IAb8BtGBbviXKMDNNeOUPgNH';
    script.onload = () => {
      mapLoaded.value = true;
      resolve();
    };
    script.onerror = reject;
    document.head.appendChild(script);
  });
};
// 图片图标配置（根据点类型设置不同图片）
const iconConfig = {
  0: {
    url: new URL('/src/assets/images/dt001x.png', import.meta.url).href, // 市中心点图片路径
    width: 20,
    height: 31
  },
  1: {
    url: new URL('/src/assets/images/dt002.png', import.meta.url).href, // 乡镇点图片路径
    width: 20,
    height: 31
  }
};
// 创建图片图标
const createImageIcon = (type) => {
  const config = iconConfig[type];
  if (!config) {
    console.error(`未找到类型为${type}的图标配置`);
    return null;
  }
  return new window.BMapGL.Icon(config.url, new window.BMapGL.Size(config.width, config.height));
};

// 添加图片标记点1
const addImageMarkers = () => {
  if (!mapInstance) return;
  shulanPoints.value.forEach((point) => {
    // 1. 创建百度地图坐标点（注意是逗号分隔经纬度！）
    const markerPoint = new window.BMapGL.Point(point.lng, point.lat);

    // 2. 处理 devList（假设是数组，遍历拼接信息窗口内容）
    let devListHtml = '';
    if (point.devList) {
      // 用 map 遍历，把每个 item 转成 HTML 片段，再用 join 拼接成字符串
      devListHtml = point.devList
        .map((item) => {
          return `<div style="padding:5px;">
                <p>${item.mc}:${item.sl}</p>
              </div>`;
        })
        .join(''); // 拼接多个 item 的 HTML
    } else {
      devListHtml = '<div style="padding:5px;">暂无设备数据</div>';
    }

    // 3. 创建图标（你原来的 createImageIcon 逻辑，这里假设能正常返回 Icon）
    const icon = createImageIcon(point.type);

    // 4. 创建标记 & 信息窗口
    if (icon) {
      const marker = new window.BMapGL.Marker(markerPoint, {
        icon
      });

      // 5. 绑定点击事件，打开自定义弹窗
      marker.addEventListener('click', () => {
        // 计算弹窗位置（将地图坐标转换为屏幕坐标）
        const pixel = mapInstance.pointToPixel(markerPoint);
        const mapContainer = document.getElementById('container');
        const rect = mapContainer.getBoundingClientRect();

        // 设置弹窗位置
        customInfoWindowStyle.value = {
          left: `${pixel.x + rect.left}px`,
          top: `${pixel.y + rect.top - 200}px` // 向上偏移200px
        };

        // 显示自定义弹窗
        selectedPoint.value = point;

        mapId.value = selectedPoint.value.id;

        showCustomInfoWindow.value = true;
        showCustomInfoWindow2.value = false;
      });

      // 6. 把标记添加到地图
      mapInstance.addOverlay(marker);
    }
  });
};
// 添加设备点标记2
const addDeviceMarkers = () => {
  if (!mapInstance) return;
  shulanPoints2.value.forEach((device) => {
    const lng = device.lnt;
    const lat = device.lat;
    if (!lng || !lat) return;
    const iconConf = deviceTypeIconMap[device.device_type];
    if (!iconConf) return;
    const point = new window.BMapGL.Point(Number(lng), Number(lat));
    const icon = new window.BMapGL.Icon(iconConf.url, new window.BMapGL.Size(iconConf.width, iconConf.height), {
      anchor: new window.BMapGL.Size(iconConf.width / 2, iconConf.height),
      imageOffset: new window.BMapGL.Size(0, 0)
    });
    const marker = new window.BMapGL.Marker(point, { icon });
    let devListHtml = '';
    if (point.devList) {
      // 用 map 遍历，把每个 item 转成 HTML 片段，再用 join 拼接成字符串
      devListHtml = point.devList
        .map((item) => {
          return `<div style="padding:5px;">
                <p>${item.mc}:${item.sl}</p>
              </div>`;
        })
        .join(''); // 拼接多个 item 的 HTML
    } else {
      devListHtml = '<div style="padding:5px;">暂无设备数据</div>';
    }
    if (icon) {
      marker.addEventListener('click', () => {
        // 计算弹窗位置（将地图坐标转换为屏幕坐标）
        const pixel = mapInstance.pointToPixel(point);
        const mapContainer = document.getElementById('container');
        const rect = mapContainer.getBoundingClientRect();

        // 设置弹窗位置
        customInfoWindowStyle.value = {
          left: `${pixel.x + rect.left}px`,
          top: `${pixel.y + rect.top - 200}px` // 向上偏移200px
        };

        // 显示自定义弹窗
        selectedPoint2.value = device;

        mapId.value = selectedPoint2.value.id;
        showCustomInfoWindow.value = false;
        showCustomInfoWindow2.value = true;
      });

      // 6. 把标记添加到地图
      mapInstance.addOverlay(marker);
    }
  });
};
// 绘制电子围栏
const addFenceMarkers = () => {
  if (!mapInstance) return;
  shulanPoints3.value.forEach((item) => {
    let fenceArr = [];
    try {
      fenceArr = typeof item.fenceLocation === 'string' ? JSON.parse(item.fenceLocation) : item.fenceLocation;
    } catch (e) {
      fenceArr = [];
    }
    if (!Array.isArray(fenceArr) || fenceArr.length < 2) return;

    // 绘制栅栏形状 - 使用多条线段
    for (let i = 0; i < fenceArr.length - 1; i++) {
      const startPoint = new window.BMapGL.Point(Number(fenceArr[i][0]), Number(fenceArr[i][1]));
      const endPoint = new window.BMapGL.Point(Number(fenceArr[i + 1][0]), Number(fenceArr[i + 1][1]));

      const polyline = new window.BMapGL.Polyline([startPoint, endPoint], {
        strokeColor: '#ff4d4f',
        strokeWeight: 3,
        strokeOpacity: 0.8,
        strokeStyle: 'dashed' // 虚线样式，更像栅栏
      });

      // 鼠标移入高亮
      polyline.addEventListener('mouseover', function () {
        polyline.setStrokeColor('#ff0000');
        polyline.setStrokeWeight(5);
        polyline.setStrokeOpacity(1);
      });
      // 鼠标移出恢复
      polyline.addEventListener('mouseout', function () {
        polyline.setStrokeColor('#ff4d4f');
        polyline.setStrokeWeight(3);
        polyline.setStrokeOpacity(0.8);
      });

      mapInstance.addOverlay(polyline);
    }

    // 连接最后一个点和第一个点，形成闭合栅栏
    if (fenceArr.length > 2) {
      const lastPoint = new window.BMapGL.Point(
        Number(fenceArr[fenceArr.length - 1][0]),
        Number(fenceArr[fenceArr.length - 1][1])
      );
      const firstPoint = new window.BMapGL.Point(Number(fenceArr[0][0]), Number(fenceArr[0][1]));

      const closingLine = new window.BMapGL.Polyline([lastPoint, firstPoint], {
        strokeColor: '#ff4d4f',
        strokeWeight: 3,
        strokeOpacity: 0.8,
        strokeStyle: 'dashed'
      });

      // 鼠标移入高亮
      closingLine.addEventListener('mouseover', function () {
        closingLine.setStrokeColor('#ff0000');
        closingLine.setStrokeWeight(5);
        closingLine.setStrokeOpacity(1);
      });
      // 鼠标移出恢复
      closingLine.addEventListener('mouseout', function () {
        closingLine.setStrokeColor('#ff4d4f');
        closingLine.setStrokeWeight(3);
        closingLine.setStrokeOpacity(0.8);
      });

      mapInstance.addOverlay(closingLine);
    }
  });
};
// 初始化地图
const initMap = async () => {
  await loadBMapScript();

  nextTick(() => {
    mapInstance = new window.BMapGL.Map('container');
    const point = new window.BMapGL.Point(126.965607, 44.406106); // 中心点
    mapInstance.centerAndZoom(point, 10);
    mapInstance.enableScrollWheelZoom();
    mapInstance.setTilt(30);

    // 设置为卫星地图
    mapInstance.setMapType(window.BMAP_SATELLITE_MAP);
    handleMapZoom(); // 绑定缩放事件
  });
};

// 组件挂载后初始化地图
onMounted(() => {
  initMap().catch((error) => {
    console.error('百度地图加载失败:', error);
  });
});

//地图

// ChatPanel 配置
const chatConfig = ref({
  ip: '************',
  title: '参管家',
  wsPort: 8282,
  httpPort: 8383,
  showTriggerButton: false
});

// 获取组件引用
const chatPanel = ref(null);
const shulanPoints2 = ref([]);
// 打开面板方法
const openChatPanel = () => {
  chatPanel.value?.openPanel();
};

// 关闭自定义弹窗
const closeCustomInfoWindow = () => {
  showCustomInfoWindow.value = false;
  showCustomInfoWindow2.value = false;
  selectedPoint.value = null;
  selectedPoint2.value = null;
};

const dashboardConfig = ref({
  offlineNum: 0,
  onlineNum: 0,
  total: 0
});
const homeStatsConfig = ref([]);
const getList = async () => {
  try {
    // 获取环境数据
    const res = await getEnvironmentData();
    if (res.code === 200) {
      chartData.swiperConfig.data = res.data;
    }

    // 获取智慧预测数据
    const res2 = await getWisdomPrediction();
    if (res2.code === 200) {
      dataList01.value = res2.data;
      console.log(res2.data,'444444444')
    }

    // 获取设备离线告警数据
    const res3 = await getDeviceOfflineAlarms();
    if (res3.code === 200) {
      dataList02.value = res3.data;
    }
    const res4 = await getAbnormalWarnings();
    if (res4.code === 200) {
      chartData.scrollListConfig.data = res4.data;
    }
    const res5 = await getWeatherTrend();
    if (res5.code === 200) {
      chartData.lineChartConfig.data = res5.data;
    }
    const res6 = await getEnvironmentAnalysis();
    if (res6.code === 200) {
      //只拿到前六条
      chartData.barListConfig.data = res6.data.slice(0, 6);
    }
    const res7 = await getDeviceOnlineStats();
    if (res7.code === 200) {
      dashboardConfig.value.offlineNum = res7.data.offlineNum;
      dashboardConfig.value.onlineNum = res7.data.onlineNum;
      dashboardConfig.value.total = res7.data.total;
    }

    const res8 = await getHomeStats();
    if (res8.code === 200) {
      homeStatsConfig.value = res8.data;
    }
    if (mapInstance) {
      mapInstance.clearOverlays();
      const res9 = await getMapData();
      if (res9.code === 200) {
        shulanPoints.value = res9.data;
        addImageMarkers();
      }
      const res10 = await getDeviceListByBaseId();
      if (res10.code === 200) {
        shulanPoints2.value = res10.data;
        addDeviceMarkers();
      }
      const res11 = await getFenceList();
      console.log('%c [ res11 ]-762', 'font-size:13px; background:pink; color:#bf2c9f;', res11);
      if (res11.code === 200) {
        shulanPoints3.value = res11.data;
        addFenceMarkers();
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// 添加定时刷新
let timer = null;

onMounted(async () => {
  getList();
  // 每30秒刷新一次数据
  timer = setInterval(getList, 30000);
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style lang="scss" scoped>
.map-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
}

.videoBox {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;

  li {
    width: 48%;
    margin: 0 1%;
    background: url('@/assets/images/video.png') no-repeat;
    background-size: 100% 100%;
    height: 46%;
    padding: 1%;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.d-player-wrap {
  width: 100%;
  height: 100%;
}

// 自定义弹窗样式
.customInfoWindow {
  position: fixed;
  z-index: 9999;
  background: rgba(20, 24, 38, 0.95);
  color: #e6f7ff;
  border-radius: 12px;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.8);
  border: 1px solid #2a3040;
  width: 350px;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease-out;

  .customInfoContent {
    padding: 16px 20px;

    .customInfoHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h3 {
        color: #40a9ff;
        font-weight: 600;
        font-size: 16px;
        margin: 0;
      }

      .closeBtn {
        background: none;
        border: none;
        color: #90caf9;
        font-size: 20px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s;

        &:hover {
          background: rgba(144, 202, 249, 0.1);
          color: #e6f7ff;
        }
      }
    }

    .customInfoBody {
      font-size: 14px;
      line-height: 1.6;

      hr {
        border: none;
        border-top: 1px solid #2a3040;
        margin: 8px 0 12px 0;
      }

      .devList {
        // height: 200px;
        overflow-y: auto;

        .devItem {
          padding: 6px 0;
          border-bottom: 1px solid rgba(42, 48, 64, 0.5);

          &:last-child {
            border-bottom: none;
          }

          p {
            margin: 0;
            color: #90caf9;
            font-weight: 500;
          }
        }
      }

      .noData {
        color: #909399;
        text-align: center;
        padding: 20px 0;
        font-style: italic;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
