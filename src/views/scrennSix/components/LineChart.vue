<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  },
  value: {
    type: String,
    default: '人参酒' // 假设value为产品名称，不同产品对应不同单位
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
// 监听 value 变化，重新渲染图表（关键：确保单位随value同步更新）
watch(
  () => props.value,
  (newVal) => {
    // console.log('value 发生变化:', newVal);
    initChart(); // value变化时重新初始化图表，更新单位
  }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);
    // 实际场景可根据需求修改单位映射关系
  const getUnitByValue = (value) => {
    const unitMap = {
      '人参酒': '吨',
      '人参茶': '袋',
      '口服液': '瓶',
      '冻干粉': '瓶',
      '人参饮片': '瓶'
    };
    return unitMap[value] || ''; // 默认单位
  };

  // 动态获取当前单位
  const currentUnit = getUnitByValue(props.value);


  // 图表配置，使用保存的初始数据
  const option = {
    color: ['#20bc8e', '#e9cc10', '#a24747'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
     formatter: (params) => {
        return `${params[0].name}: ${params[0].value}${currentUnit}`;
      }
    },
    legend: {
      data: ['计划量', '实际量'],
      textStyle: {
        color: '#ffffff'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%', // 适当增加底部距离，避免标签被截断
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.config.data.map((item) => item.month),
      axisLine: {
        lineStyle: {
          color: 'rgba(66, 192, 255, .3)'
        }
      },
      axisLabel: {
        color: '#fffffff',
        textStyle: {
          fontSize: 12
        },
        interval: 0, // 关键配置：强制显示所有标签（0表示全部显示）
        // 可选：如果标签过挤，可设置旋转角度
        rotate: 0 // 0-90度，根据需要调整（例如rotate: 30）
      },
      axisTick: {
        // 刻度
        show: false
      }
    },
    yAxis: [
      {
         name: '单位：'+currentUnit, 
        nameTextStyle: { 
          color: '#ffffff', 
          fontSize: 12, 
          padding: [0, 0, 0, 5] 
        },
        axisLabel: {
          formatter: '{value}',
          color: '#ffffff',
          textStyle: {
            fontSize: 12
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(66, 192, 255, .3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(66, 192, 255, .3)'
          }
        }
      }
    ],
    series: [
      {
        name: '计划量',
        type: 'line',
        data: props.config.data.map((item) => item.plan),
        smooth: 0.5,
        symbol: 'circle'
      },
      {
        name: '实际量',
        type: 'line',
        data: props.config.data.map((item) => item.real),
        smooth: 0.5,
        symbol: 'circle'
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 7000);
};

onMounted(() => {
  nextTick(() => {
    initChart(); // 首次初始化
    resizeChart();

    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时器
  });
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
