<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  const option = {
    grid: {
      top: '30',
      left: '1%',
      right: '1%',
      bottom: '8%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: (params) => {
        return `${params[0].name}: ${params[0].value}万度`;
      }
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: true,
        axisLine: {
          show: false
        },
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: 12
          }
        },
        axisTick: {
          show: false
        },
        data: props.config.data.map((item) => item.month)
      }
    ],
    yAxis: [
      {
        name: '单位：万度', // 新增：y 轴单位
        nameTextStyle: { 
          color: '#ffffff', 
          fontSize: 12, 
          padding: [0, 0, 0, 15] 
        },
        type: 'value',
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'solid',
            color: 'rgba(66, 192, 255, .3)',
            width: 1
          }
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgb(2,185,208)'
          }
        },
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: 12
          }
        }
      }
    ],
    series: [
      {
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showAllSymbol: true,
        symbolSize: 4,
        label: {
          show: true,
          position: 'top'
        },
        itemStyle: {
          normal: {
            color: '#a24747'
          }
        },

        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(162, 71, 71, 0.38)'
                },
                {
                  offset: 1,
                  color: 'rgba(162, 71, 71, 0.11)'
                }
              ],
              false
            )
          }
        },
        data: props.config.data.map((item) => item.nh)
      }
    ]
  };
  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 9000);
};

onMounted(() => {
  nextTick(() => {
    initChart(); // 首次初始化
    resizeChart();

    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时器
  });
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>
