<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});

// 监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 获取数据项的单位
const getUnit = (name) => {
  if (name === '冻干粉') return '万瓶';
  if (name === '冻干饮片') return '万瓶';
  if (name === '人参茶') return '万袋';
  if (name === '口服液') return '万瓶';
  if (name === '人参酒') return '吨';
  if (name === '人参饮片') return '万瓶';
  return '';
};

// 格式化标签显示
const formatLabel = (params) => {
  const unit = getUnit(params.name);
  let value = params.value;
  if (params.name == '口服液') {
    value = params.value;
  }
  if (params.name == '人参茶') {
    value = params.value * 100;
  }
  if (params.name == '人参酒') {
    value = params.value * 100;
  }

  return `${params.name}: ${value}${unit}`;
};

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置
  const option = {
    color: ['#19d9fe', '#1978e6', '#20bc8e', '#e9cc10', '#a24747', '#6b47a2'],
    tooltip: {
      trigger: 'item',
      formatter: (params) => formatLabel(params),
      backgroundColor: 'rgba(20,40,80,0.85)',
      borderColor: 'rgba(80,180,255,0.5)',
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      }
    },
    series: [
      {
        name: '人参存量统计',
        type: 'pie',
        radius: [30, 80],
        center: ['50%', '50%'], // 调整为居中显示
        roseType: 'area',
        itemStyle: {
          borderRadius: 8
        },
        label: {
          show: true,
          color: '#ffffff',
          formatter: (params) => formatLabel(params),
          fontSize: 12, // 增加标签字体大小
          padding: [5, 5] // 增加标签内边距
        },
        data: props.config.data
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每8秒重新加载一次图表
  reloadTimer = setInterval(initChart, 8000);
};

onMounted(() => {
  nextTick(() => {
    initChart(); // 首次初始化
    resizeChart();

    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时器
  });
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>
