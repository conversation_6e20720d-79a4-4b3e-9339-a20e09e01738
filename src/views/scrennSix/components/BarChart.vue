<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  },
  value: {
    type: String,
    default: '人参酒' // 假设value为产品名称，不同产品对应不同单位
  }
});

// 监听 data 变化，重新渲染图表
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);

// 监听 value 变化，重新渲染图表（关键：确保单位随value同步更新）
watch(
  () => props.value,
  (newVal) => {
    console.log('value 发生变化:', newVal);
    initChart(); // value变化时重新初始化图表，更新单位
  }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0;

// 初始化图表（动态使用 props.value 控制单位）
const initChart = () => {
  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  chartInstance = echarts.init(chartContainer.value);

  // 根据 props.value 动态确定单位（示例逻辑：不同产品对应不同单位）
  // 实际场景可根据需求修改单位映射关系
  const getUnitByValue = (value) => {
    const unitMap = {
      '人参酒': '吨',
      '人参茶': '袋',
      '口服液': '瓶',
      '冻干粉': '瓶',
      '人参饮片': '瓶'
    };
    return unitMap[value] || ''; // 默认单位
  };

  // 动态获取当前单位
  const currentUnit = getUnitByValue(props.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)',
      borderColor: 'rgba(80,180,255,0.5)',
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      axisPointer: {
        type: 'shadow'
      },
      // tooltip 中显示动态单位（关键：使用currentUnit）
      formatter: (params) => {
        return `${params[0].name}: ${params[0].value}${currentUnit}`;
      }
    },
    grid: {
      top: '15%',
      right: '5%',
      left: '5%',
      bottom: '20%'
    },
    xAxis: [
      {
        type: 'category',
        data: props.config.data.map((item) => item.month),
        axisLine: {
          lineStyle: {
            color: 'rgba(66, 192, 255, .3)'
          }
        },
        axisLabel: {
          color: '#ffffff',
          textStyle: { fontSize: 12 },
          interval: 0
        },
        axisTick: { show: false }
      }
    ],
    yAxis: [
      {
           name: '单位：'+currentUnit, 
        nameTextStyle: { 
          color: '#ffffff', 
          fontSize: 12, 
          padding: [0, 0, 0, 25] 
        },
        axisLabel: {
          // Y轴刻度显示动态单位（关键：使用currentUnit）
          formatter: `{value}`,
          color: '#ffffff',
          textStyle: { fontSize: 12 }
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: { color: 'rgba(66, 192, 255, .3)' }
        },
        splitLine: {
          lineStyle: { color: 'rgba(66, 192, 255, .3)' }
        }
      }
    ],
    series: [
      {
        type: 'bar',
      label: {
        show: true, // 开启显示数值标签
        position: 'top', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
        textStyle: {
          color: 'rgba(32, 188, 142, 1)', // 标签文字颜色，可按需调整
          fontSize: 12 // 标签文字大小，可按需调整
        }
      },
        data: props.config.data.map((item) => item.cl),
        barWidth: '10',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [
                { offset: 0, color: 'rgba(32, 188, 142, 1)' },
                { offset: 0.98, color: 'rgba(32, 188, 142, 0)' }
              ],
              false
            ),
            shadowColor: '#20bc8e',
            shadowBlur: 4
          }
        }
      }
    ]
  };

  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  reloadTimer = setInterval(initChart, 12000);
};

onMounted(() => {
  nextTick(() => {
    initChart();
    resizeChart();
    window.addEventListener('resize', handleResize);
    startReloadTimer();
  });
});

onBeforeUnmount(() => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>