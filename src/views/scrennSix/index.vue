<!--suppress ALL -->
<template>
  <div class="pictureImg">
    <!-- 按钮 -->
    <div class="floatingBtn float-animation" @click="openChatPanel" v-if="isVisible">
      <img src="@/assets/images/Floating.gif" />
      <h2>参管家</h2>
    </div>
    <!-- 按钮 -->
    <!-- 聊天面板 -->
    <ChatPanel ref="chatPanel" :config="chatConfig" :showTriggerButton="false" v-if="isVisible" />
    <!-- 标题 -->
    <ScrennHeader v-if="isVisible" />
    <!-- 标题 -->
    <!-- 内容展示 -->
    <div class="sixLeft" v-if="isVisible">
      <div class="columnItem columnItemxx">
        <div class="boxTitle">
          <h2><i></i>加工类比统计</h2>
          <img src="@/assets/images/title_line.png" />
        </div>
        <div class="contentBox">
          <RoseChart :config="chartData.roseChartConfig" />
        </div>
      </div>
      <div class="columnItem columnItemxx">
        <div class="boxTitle">
          <h2><i></i>加工人参缺陷分析</h2>
          <img src="@/assets/images/title_line.png" />
        </div>
        <div class="contentBox">
          <RadarChart :config="chartData.radarChartConfig" />
        </div>
      </div>
      <div class="columnItem columnItemxx" style="margin-bottom: 0">
        <div class="boxTitlex">
          <div class="Title">
            <h2><i></i>加工产量统计</h2>
            <div class="big-data-select-container">
              <el-select
                size="small"
                v-model="optionValue"
                placeholder="请选择"
                style="width: 110px"
                class="custom-blue-select"
                @change="changeSelect"
              >
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div>
          <img src="@/assets/images/title_line.png" />
        </div>
        <div class="contentBox">
          <BarChart :config="chartData.barChartConfig" :value="optionValue" />
        </div>
      </div>
    </div>
    <div class="sixTop" v-if="isVisible">
      <ul class="scrennSixData" style="height: 100%">
        <li>
          <img src="@/assets/images/six06.png" />
          <div class="textBox">
            <h2 class="text">加工主体</h2>
              <h3>{{ topData.factoryCount }}<i>个</i></h3>
          </div>
        </li>
        <li>
          <img src="@/assets/images/six07.png" />
          <div class="textBox">
            <h2 class="text">加工量</h2>
            <h3>{{ topData.cl }}<i>万株</i></h3>
          </div>
        </li>
        <li>
          <img src="@/assets/images/six08.png" />
          <div class="textBox">
            <h2 class="text">产值</h2>
            <h3>{{ topData.cz }}<i>亿元</i></h3>
          </div>
        </li>
        <li>
          <img src="@/assets/images/six09.png" />
          <div class="textBox">
            <h2 class="text">任务量</h2>
            <h3>{{ topData.planCount }}<i>万条</i></h3>
          </div>
        </li>
      </ul>
    </div>
    <div class="sixBottom" v-if="isVisible">
      <ul class="scrennSixData dataBg" style="position: absolute; bottom: 0; height: auto">
        <li class="sixBgC01">
          <h3>{{ dataObj1.total_jgl * 100 }}<span style="font-size: 14px">吨</span></h3>
          <h2>{{ dataObj1.product_name }}</h2>
        </li>
        <li class="sixBgC02">
          <h3>{{ dataObj2.total_jgl * 100 }}<span style="font-size: 14px">万袋</span></h3>
          <h2>{{ dataObj2.product_name }}</h2>
        </li>
        <li class="sixBgC03">
          <h3>{{ dataObj3.total_jgl }}<span style="font-size: 14px">万瓶</span></h3>
          <h2>{{ dataObj3.product_name }}</h2>
        </li>
        <li class="sixBgC04">
          <h3>{{ dataObj4.total_jgl }}<span style="font-size: 14px">万瓶</span></h3>
          <h2>{{ dataObj4.product_name }}</h2>
        </li>
        <li class="sixBgC05">
          <h3>{{ dataObj5.total_jgl * 100 }}<span style="font-size: 14px">万瓶</span></h3>
          <h2>{{ dataObj5.product_name }}</h2>
        </li>
      </ul>
    </div>
    <div class="sixRight" v-if="isVisible">
      <div class="columnItem columnItemxx">
        <div class="boxTitlex">
          <div class="Title">
            <h2><i></i>加工生产进度统计</h2>
            <div class="big-data-select-container">
              <el-select
                size="small"
                v-model="optionValueX"
                placeholder="请选择"
                style="width: 110px"
                class="custom-blue-select"
                @change="changeSelectX"
              >
                <el-option v-for="item in optionsX" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div>
          <img src="@/assets/images/title_line.png" />
        </div>
        <div class="contentBox">
          <LineChart :config="chartData.lineChartConfig" :value="optionValueX" />
        </div>
      </div>
      <div class="columnItem columnItemxx">
        <div class="boxTitle">
          <h2><i></i>加工厂家能耗统计</h2>
          <img src="@/assets/images/title_line.png" />
        </div>
        <div class="contentBox">
          <AreaChart :config="chartData.areaChartConfig" />
        </div>
      </div>
      <div class="columnItem columnItemxx" style="margin-bottom: 0">
        <div class="boxTitle">
          <h2><i></i>加工成品库存统计</h2>
          <img src="@/assets/images/title_line.png" />
        </div>
        <div class="contentBox">
          <BarChart2 :config="chartData.barChart2Config" />
        </div>
      </div>
    </div>
    <!-- 工厂 -->
    <div
      class="modelImgBox"
      style="position: absolute; top: 0; left: 0; background-color: #ffffff; height: 99%; width: 100%"
    >
      <iframe :src="iframeSrc" frameborder="0" width="100%" height="100%"></iframe>
    </div>
    <!-- 工厂 -->
    <!-- 边框线 -->
    <div class="pictureSix" v-if="isVisible"></div>
    <!-- 边框线 -->
  </div>
</template>

<script setup>
import ScrennHeader from '@/components/ScrennHeader';
import BarChart from './components/BarChart.vue';
import BarChart2 from './components/BarChart2.vue';
import RadarChart from './components/RadarChart.vue';
import LineChart from './components/LineChart.vue';
import RoseChart from './components/RoseChart.vue';
import AreaChart from './components/areaChart.vue';
import ChatPanel from '../../components/ChatPanel/index.vue';
import { ref } from 'vue';
import {
  getProcessingComparison,
  getDefectAnalysis,
  getProductionStats,
  getTopStatistics,
  getProductionProgress,
  getEnergyConsumption,
  getInventoryStats
} from '@/api/screen/index5';

//select1
const optionValue = ref('人参酒');
const options = ref([
  { value: '人参酒', label: '人参酒' },
  { value: '人参茶', label: '人参茶' },
  { value: '口服液', label: '口服液' },
  { value: '人参饮片', label: '人参饮片' },
  { value: '冻干粉', label: '冻干粉' }
]);
const changeSelect = async (value) => {
  const Result = await getProductionStats(value);
  chartData.barChartConfig.data = Result.data;
  optionValue.value = value;
};
//select1

//select1
const optionValueX = ref('人参酒');
const optionsX = ref([
  { value: '人参酒', label: '人参酒' },
  { value: '人参茶', label: '人参茶' },
  { value: '口服液', label: '口服液' },
  { value: '人参饮片', label: '人参饮片' },
  { value: '冻干粉', label: '冻干粉' }
]);
const changeSelectX = async (value) => {
  const Result = await getProductionProgress(value);
  chartData.lineChartConfig.data = Result.data.map((item) => ({
    ...item,
    plan: Math.floor(Number(item.plan))
  }));
  optionValueX.value = value;
};
//select1
//键盘事件 Ctrl+/ 显示隐藏
// 控制显示状态
const isVisible = ref(true);
// 处理键盘事件的函数
const handleKeyDown = (e) => {
  // 检查是否同时按下了 Ctrl 和 /
  console.log(e.ctrlKey, '点击');
  // 注意：/ 键在键盘上可能需要配合 Shift，所以 key 可能是 '/' 或 '?'
  if (e.ctrlKey && (e.key === '/' || e.key === '?')) {
    e.preventDefault(); // 阻止默认行为（如浏览器的查找功能）
    // 切换显示状态
    isVisible.value = !isVisible.value;
  }
  getData();
};
// 挂载时添加事件监听
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
});

// 卸载时移除事件监听，防止内存泄漏
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown);
});

const iframeSrc = ref('/Builds/index.html');
const dataObj1 = ref({});
const dataObj2 = ref({});
const dataObj3 = ref({});
const dataObj4 = ref({});
const dataObj5 = ref({});

// 各个图表的数据
const chartData = reactive({
  roseChartConfig: {
    data: []
  },
  radarChartConfig: {
    data: []
  },
  barChartConfig: {
    data: []
  },
  lineChartConfig: {
    data: []
  },
  areaChartConfig: {
    data: []
  },
  barChart2Config: {
    data: []
  }
});
// ChatPanel 配置
const chatConfig = ref({
  ip: '************',
  title: '参管家',
  wsPort: 8282,
  httpPort: 8383,
  showTriggerButton: false
});

// 获取组件引用
const chatPanel = ref(null);

// 打开面板方法
const openChatPanel = () => {
  chatPanel.value?.openPanel();
};
const blackPan = ref({
  product_name1: 0,
  product_name2: 0,
  product_name3: 0,
  product_name4: 0,
  product_name5: 0,
  value1: 0,
  value2: 0,
  value3: 0,
  value4: 0,
  value5: 0
});
const topData = ref({});
const getData = async () => {
  const res = await getProcessingComparison();
  chartData.roseChartConfig.data = res.data.map((item) => ({
    name: item.product_name,
    value: item.total_jgl
  }));
  dataObj1.value = res.data[0];
  dataObj2.value = res.data[1];
  dataObj3.value = res.data[2];
  dataObj4.value = res.data[3];
  dataObj5.value = res.data[4];
  console.log(res.data, 'sssssssss');
  res.data.forEach((item) => {
    if (item.product_name === '人参茶') {
      blackPan.value.product_name1 = item.product_name;
      blackPan.value.value1 = item.total_jgl;
    }
    if (item.product_name === '人参酒') {
      blackPan.value.product_name2 = item.product_name;
      blackPan.value.value2 = item.total_jgl;
    }
    if (item.product_name === '冻干粉') {
      blackPan.value.product_name3 = item.product_name;
      blackPan.value.value3 = item.total_jgl;
    }
    if (item.product_name === '口服液') {
      blackPan.value.product_name3 = item.product_name;
      blackPan.value.value4 = item.total_jgl;
    }
    if (item.product_name === '黑参片') {
      blackPan.value.product_name3 = item.product_name;
      blackPan.value.value5 = item.total_jgl;
    }
  });

  const res2 = await getDefectAnalysis();
  chartData.radarChartConfig.data = res2.data.map((item) => ({
    value: item.products,
    year: item.year
  }));

  const res3 = await getProductionStats();
  chartData.barChartConfig.data = res3.data;

  const res4 = await getProductionProgress();
  chartData.lineChartConfig.data = res4.data.map((item) => ({
    ...item,
    plan: Math.floor(Number(item.plan))
  }));

  const res5 = await getEnergyConsumption();
  chartData.areaChartConfig.data = res5.data;
  const res7 = await getTopStatistics();
  topData.value = res7.data;

  const res6 = await getInventoryStats();
  chartData.barChart2Config.data = res6.data.map((item) => ({
    name: item.month,
    value: item.kc
  }));
};
onMounted(() => {
  getData();
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
:deep(.el-select__wrapper) {
  background-color: #74d5ff63 !important;
  box-shadow: 0 0 0 1px #74d5ff inset;
}

:deep(.el-input-group__append) {
  background-color: #001033 !important;
  color: #74d5ff;
}

:deep(.el-input-group__prepend) {
  background-color: #001033 !important;
  // box-shadow: 0 1px 0 0 #3164b1 inset,
  //  0 -1px 0 0 #3164b1 inset,
  //   -1px 0 0 0 #3164b1 inset;
}

:deep(.el-input__wrapper) {
  box-shadow:
    0 1px 0 0 #74d5ff inset,
    0 -1px 0 0 #74d5ff inset,
    -1px 0 0 0 #74d5ff inset;
}

:deep(.el-select__placeholder) {
  color: #74d5ff;
}

:deep(.el-input__placeholder) {
  color: #74d5ff;
}

:deep(.el-input-group__append) {
  box-shadow:
    0 1px 0 0 #74d5ff inset,
    0 -1px 0 0 #74d5ff inset,
    -1px 0 0 0 #74d5ff inset;
}

:deep(.el-select__placeholder.is-transparent) {
  color: #74d5ff;
}

:deep(.el-select__caret) {
  color: #74d5ff;
}

:deep(.el-input-group--prepend .el-input-group__prepend .el-select .el-select__wrapper) {
  box-shadow:
    0 1px 0 0 #74d5ff inset,
    0 -1px 0 0 #74d5ff inset,
    -1px 0 0 0 #74d5ff inset;
}

:deep(.el-input__inner) {
  color: #74d5ff !important;
}

.scrennSixData {
  width: 100%;
  height: 30%;
  display: flex;
  justify-content: space-around;

  li {
    width: 25%;
    display: flex;
    justify-content: flex-start;

    img {
      width: 80px;
      height: 76px;
      margin-right: 5px;
    }

    // height: 76px;
    // background: url('@/assets/images/u1343.png') no-repeat;
    // background-size: 100% 100%;
    // display: flex;
    // justify-content: space-between;
    // padding: 15px 20px;
    .text {
      color: #fff;
      font-size: 16px;
      margin: 0;
      margin-top: 15px;
      background: url('@/assets/images/sixx.png') no-repeat;
      background-size: 100% 100%;
      width: 80px;
      height: 30px;
      line-height: 30px;
      padding-left: 5px;
    }

    h3 {
      color: #e5e5e5;
      font-size: 24px;
      font-weight: normal;

      i {
        font-style: normal;
        font-size: 14px;
      }
    }
  }
}

.sixBgC01 {
  background: url('@/assets/images/six01.png') no-repeat !important;

  h2 {
    font-size: 16px;
    color: #ddc7e6 !important;
  }

  h3 {
    font-size: 20px;
    margin-top: 12px;
    text-align: center;
    color: #ddc7e6 !important;
  }
}

.sixBgC02 {
  background: url('@/assets/images/six02.png') no-repeat !important;

  h2 {
    font-size: 16px;
    color: #97cdd6 !important;
  }

  h3 {
    font-size: 20px;
    margin-top: 12px;
    color: #97cdd6 !important;
  }
}

.sixBgC03 {
  background: url('@/assets/images/six03.png') no-repeat !important;

  h2 {
    font-size: 16px;
    color: #a7d4b7 !important;
  }

  h3 {
    font-size: 20px;
    margin-top: 12px;
    color: #a7d4b7 !important;
  }
}

.sixBgC04 {
  background: url('@/assets/images/six04.png') no-repeat !important;

  h2 {
    font-size: 16px;
    color: #cabe90 !important;
  }

  h3 {
    font-size: 20px;
    margin-top: 12px;
    color: #cabe90 !important;
  }
}

.sixBgC05 {
  background: url('@/assets/images/six05.png') no-repeat !important;

  h2 {
    font-size: 16px;
    color: #bdc8e9 !important;
  }

  h3 {
    font-size: 20px;
    margin-top: 12px;
    color: #bdc8e9 !important;
  }
}

.dataBg li {
  display: block;
  width: 132px;
  height: 108px;
  text-align: center;
}

.contentBox {
  height: 77%;
}
</style>
