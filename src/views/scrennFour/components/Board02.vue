<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch, computed } from 'vue';
import * as echarts from 'echarts';

// 接收父组件传递的配置数据
const props = defineProps({
  config: {
    type: Object,
    default: () => ({ data: { value: 0 }, title: "仪表盘" }),
    required: true
  }
});

// 动态计算当前值（随父组件数据实时更新）
const currentValue = computed(() => {
  return props.config.data?.value || 0;
});

// 颜色配置
const colorSet = { color: "#1c9083" };
const color1 = {
  type: "linear",
  x: 0, y: 0, x2: 1, y2: 1,
  colorStops: [
    { offset: 0, color: "rgba(255,255,255,0.1)" },
    { offset: 1, color: "rgba(255,255,255,0.3)" }
  ],
  global: false
};
const color2 = {
  type: "linear",
  x: 0, y: 0, x2: 1, y2: 1,
  colorStops: [
    { offset: 0, color: "#38aea1" },
    { offset: 1, color: "#1c9083" }
  ],
  global: false
};

// 动态生成图表配置项
const getChartOption = () => ({
  series: [
    {
      name: "内部进度条",
      type: "gauge",
      radius: "70%",
      splitNumber: 10,
      axisLine: {
        lineStyle: {
          color: [[currentValue.value / 100, colorSet.color], [1, colorSet.color]],
          width: 2
        }
      },
      axisLabel: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      itemStyle: { color: "#1c9083" },
      detail: { show: false },
      title: { show: false },
      data: [{ name: "", value: currentValue.value }],
      pointer: { show: true, length: "70%", radius: "20%", width: 3 },
      animationDuration: 4000
    },
    {
      name: "内部阴影",
      type: "gauge",
      radius: "60%",
      splitNumber: 10,
      axisLine: {
        lineStyle: {
          color: [
            [
              currentValue.value / 100,
              new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                { offset: 0, color: "rgba(22, 206, 185,0)" },
                { offset: 0.5, color: "rgba(22, 206, 185,0.2)" },
                { offset: 1, color: "rgba(22, 206, 185,1)" }
              ])
            ],
            [1, "rgba(22, 206, 185,0)"]
          ],
          width: 100
        }
      },
      axisLabel: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      itemStyle: { show: false }
    },
    {
      name: "内部小圆",
      type: "gauge",
      radius: "62%",
      splitNumber: 10,
      axisLine: {
        lineStyle: {
          color: [[currentValue.value / 100, color2], [1, "rgba(0,0,0,0)"]],
          width: 10
        }
      },
      axisLabel: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      itemStyle: { show: false }
    },
    {
      name: "外部刻度",
      type: "gauge",
      radius: "72%",
      min: 0,
      max: 100,
      splitNumber: 5,
      startAngle: 225,
      endAngle: -45,
      axisLine: {
        show: true,
        lineStyle: { width: 1, color: [[1, "rgba(0,0,0,0)"]] }
      },
      axisLabel: {
        show: true,
        color: "#1c9083",
        fontSize: 14,
        fontFamily: "SourceHanSansSC-Regular",
        fontWeight: "bold",
        distance: -25,
        formatter: v => {
          const labels = ["0", "10", "20", "30", "40", "50", "60", "70", "80", "90", "100"];
          return labels[v / 10] || "";
        }
      },
      axisTick: {
        show: true,
        splitNumber: 3,
        lineStyle: { color: color1, width: 1 },
        length: -6
      },
      splitLine: {
        show: true,
        length: -12,
        lineStyle: { color: color1 }
      },
      detail: { show: false }
    },
    {
      name: "内部进度条",
      type: "gauge",
      radius: "30%",
      splitNumber: 10,
      axisLine: {
        lineStyle: {
          color: [[currentValue.value / 100, colorSet.color], [1, colorSet.color]],
          width: 1
        }
      },
      axisLabel: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      itemStyle: { color: "#cfcae8" },
      detail: {
        formatter: value => {
          const num = Math.round(value);
          return `${parseInt(num).toFixed(0)}%`;
        },
        offsetCenter: [0, 40],
        textStyle: { padding: 0, fontSize: 14, color: "#cfcae8" }
      },
      title: {
        show: true,
        offsetCenter: [0, 55],
        textStyle: { 
          color: "#fff", 
          fontSize: 16, 
          fontWeight: 400, 
          fontFamily: "MicrosoftYaHei" 
        }
      },
      data: [{ 
        value: currentValue.value, 
        itemStyle: { color: "#cfcae8", fontFamily: "MicrosoftYaHei", fontSize: 12 } 
      }],
      pointer: { show: true, length: "70%", radius: "20%", width: 4 },
      animationDuration: 4000
    },
    {
      type: "pie",
      tooltip: { show: false },
      hoverAnimation: false,
      legendHoverLink: false,
      radius: ["0%", "6%"],
      center: ["50%", "50%"],
      label: { show: false },
      labelLine: { show: false },
      data: [{ value: 120, itemStyle: { color: "#cfcae8" } }]
    }
  ]
});

// 核心变量
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

// 图表刷新逻辑
const reloadChart = () => {
  if (!chartContainer.value) return;
  
  // 销毁旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  
  // 创建新实例并应用配置
  setTimeout(() => {
    chartInstance = echarts.init(chartContainer.value);
    chartInstance.setOption(getChartOption());
  }, 200);
};

// 定时刷新
const startReloadTimer = () => {
  reloadTimer = setInterval(reloadChart, 9000);
};

// 尺寸调整
const resizeChart = () => chartInstance?.resize();
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 监听数据变化实时更新图表
watch(
  currentValue,
  () => {
    chartInstance?.setOption(getChartOption());
  }
);

// 生命周期管理
onMounted(() => {
  nextTick(() => {
    // 打印当前组件接收的父组件数据
    console.log(`Board02组件接收的数据:`, props.config);
    reloadChart();
    window.addEventListener('resize', handleResize);
    startReloadTimer();
  });
});

onBeforeUnmount(() => {
  if (reloadTimer) clearInterval(reloadTimer);
});

onUnmounted(() => {
  if (chartInstance) chartInstance.dispose();
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style scoped>
.w-full { width: 100%; }
.h-full { height: 200px; }
</style>
