<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ data: { value2: 0 } })
  }
});

// 核心变量
const chartContainer = ref(null);
let chartInstance = null;
let num = 0; // 当前进度值
let reloadTimer = null; // 定时刷新定时器

// 监听数据变化，更新进度值
watch(
  () => props.config.data?.value2,
  (newVal) => {
    if (newVal !== undefined && !isNaN(newVal)) {
      num = Number(newVal); // 直接更新数值（无滚动动画）
      chartInstance?.setOption(getOption()); // 刷新图表
    }
  },
  { immediate: true }
);

// 生成图表配置项（无复杂动画）
const getOption = () => ({
  title: [
    {
      x: "center",
      top: "55%",
      textStyle: {
        color: "#FFFFFF",
        fontSize: 16,
        fontWeight: "100"
      }
    },
    {
      text: `${num}%`, // 直接显示当前数值
      x: "center",
      y: "center",
      textStyle: {
        fontSize: "18",
        color: "#FFFFFF"
      }
    }
  ],
  polar: {
    radius: ["42%", "62%"],
    center: ["50%", "50%"]
  },
  angleAxis: {
    max: 100,
    show: false
  },
  radiusAxis: {
    type: "category",
    show: true,
    axisLabel: { show: false },
    axisLine: { show: false },
    axisTick: { show: false }
  },
  series: [
    // 进度条（仅保留基础加载动画）
    {
      type: "bar",
      roundCap: true,
      barWidth: 30,
      showBackground: true,
      backgroundStyle: {
        color: "rgba(66, 66, 66, .3)",
        borderRadius: [15, 15, 15, 15]
      },
      data: [num], // 直接使用当前进度值
      coordinateSystem: "polar",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
          { offset: 0, color: "#16CEB9" },
          { offset: 1, color: "#16CEB9" }
        ]),
        borderRadius: [15, 15, 15, 15]
      },
      animationDuration: 500 // 简短的加载动画（从无到有）
    },
    // 外圆环
    {
      type: "pie",
      startAngle: 80,
      radius: ["70%"],
      hoverAnimation: false,
      center: ["50%", "50%"],
      itemStyle: {
        color: "rgba(66, 66, 66, .1)",
        borderWidth: 1,
        borderColor: "#16CEB9"
      },
      data: [100]
    },
    // 内圆环
    {
      type: "pie",
      startAngle: 80,
      radius: ["38%"],
      hoverAnimation: false,
      center: ["50%", "50%"],
      itemStyle: {
        color: "rgba(66, 66, 66, .1)",
        borderWidth: 1,
        borderColor: "#16CEB9"
      },
      data: [100]
    }
  ]
});

// 初始化/重新加载图表（核心：从无到有效果）
const reloadChart = () => {
  if (!chartContainer.value) return;

  // 1. 先销毁旧实例（清空图表）
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 2. 延迟200ms后重新创建（增强“从无到有”的视觉差）
  setTimeout(() => {
    chartInstance = echarts.init(chartContainer.value);
    chartInstance.setOption(getOption()); // 重新加载
  }, 200);
};

// 启动定时刷新（每3秒重新加载一次）
const startReloadTimer = () => {
  reloadTimer = setInterval(() => {
    reloadChart(); // 定时重新加载图表
  }, 3000); // 3秒刷新一次（可调整）
};

// 尺寸调整
const resizeChart = () => chartInstance?.resize();
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 生命周期
onMounted(() => {
  nextTick(() => {
    reloadChart(); // 首次加载
    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时刷新
  });
});

// 清理资源
onBeforeUnmount(() => {
  if (reloadTimer) clearInterval(reloadTimer);
});

onUnmounted(() => {
  if (chartInstance) chartInstance.dispose();
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style scoped>
.w-full { width: 100%; }
.h-full { height: 130px; }
</style>