<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    data: []
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: (params) => {
        return `${params.name}: ${params.value}公顷`;
      }
    },
    legend: {
      orient: 'horizontal',
      right: 32,
      bottom: 0,
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#74D5FF',
        fontSize: 12
      }
    },
    series: [
      {
        name: '占比',
        type: 'pie',
        radius: ['40%', '70%'],
        data: props.config.data,
        label: {
          show: true,
          color: '#74D5FF',
          formatter: '{b}:({d}%)'
        },
        itemStyle: {
          normal: {
            color: function (params) {
              var colorList = [
                {
                  c1: '#19d9fe',
                  c2: '#19d9fe'
                },
                {
                  c1: '#1978e6',
                  c2: '#1978e6'
                },
                {
                  c1: '#20bc8e',
                  c2: '#20bc8e'
                },
                {
                  c1: '#e9cc10',
                  c2: '#e9cc10'
                }
              ];
              return new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: colorList[params.dataIndex].c1
                },
                {
                  offset: 1,
                  color: colorList[params.dataIndex].c2
                }
              ]);
            }
          }
        }
      }
    ],
    graphic: {
      elements: [
        {
          type: 'image',
          z: 100, // 确保图片在最上层
          left: '38.3%',
          top: 'center',
          style: {
            image: new URL('/src/assets/images/rs.png', import.meta.url).href,
            width: 96, // 图片宽度，根据需要调整
            height: 96, // 图片高度，根据需要调整
            borderRadius: '50%' // 可选：如果需要圆形图片
          }
        }
      ]
    }
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 9000);
};

onMounted(() => {
  resizeChart();

  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>
