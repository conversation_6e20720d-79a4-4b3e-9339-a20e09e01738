<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ data: { value: 0 } }) // 默认值确保安全访问
  }
});

// 核心变量
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null; // 定时刷新定时器

// 监听数据变化，实时更新
watch(
  () => props.config.data?.value,
  (newVal) => {
    if (newVal !== undefined && !isNaN(newVal)) {
      // 数据变化时直接刷新图表
      chartInstance?.setOption(getOption());
    }
  },
  { immediate: true }
);

// 生成图表配置项（保留原有样式）
const getOption = () => ({
  title: [
    {
      x: "center",
      top: "55%",
      textStyle: {
        color: "#FFFFFF",
        fontSize: 16,
        fontWeight: "100",
      },
    },
    {
      text: `${props.config.data?.value || 0}%`, // 显示当前数值
      x: "center",
      y: "center",
      textStyle: {
        fontSize: "18",
        color: "#FFFFFF",
      },
    },
  ],
  polar: {
    radius: ["42%", "62%"],
    center: ["50%", "50%"],
  },
  angleAxis: {
    max: 100,
    show: false,
  },
  radiusAxis: {
    type: "category",
    show: true,
    axisLabel: { show: false },
    axisLine: { show: false },
    axisTick: { show: false },
  },
  series: [
    {
      name: "",
      type: "bar",
      roundCap: true,
      barWidth: 30,
      showBackground: true,
      backgroundStyle: { color: "rgba(66, 66, 66, .3)" },
      data: [props.config.data?.value || 0], // 使用最新数值
      coordinateSystem: "polar",
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: "#6648FF" },
            { offset: 1, color: "#6648FF" },
          ]),
        },
      },
      animationDuration: 500 // 简短加载动画
    },
    {
      name: "",
      type: "pie",
      startAngle: 80,
      radius: ["70%"],
      hoverAnimation: false,
      center: ["50%", "50%"],
      itemStyle: {
        color: "rgba(66, 66, 66, .1)",
        borderWidth: 1,
        borderColor: "#5269EE",
      },
      data: [100],
    },
    {
      name: "",
      type: "pie",
      startAngle: 80,
      radius: ["38%"],
      hoverAnimation: false,
      center: ["50%", "50%"],
      itemStyle: {
        color: "rgba(66, 66, 66, .1)",
        borderWidth: 1,
        borderColor: "#5269EE",
      },
      data: [100],
    },
  ],
});

// 重新加载图表（从无到有效果）
const reloadChart = () => {
  if (!chartContainer.value) return;

  // 1. 销毁旧实例（清空图表）
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 2. 延迟200ms后重新创建（增强“从无到有”的视觉差）
  setTimeout(() => {
    chartInstance = echarts.init(chartContainer.value);
    chartInstance.setOption(getOption());
  }, 200);
};

// 启动定时刷新（每3秒一次）
const startReloadTimer = () => {
  reloadTimer = setInterval(() => {
    reloadChart(); // 定时重新加载
  }, 3000); // 3秒刷新一次（可调整）
};

// 尺寸调整
const resizeChart = () => chartInstance?.resize();
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 生命周期
onMounted(() => {
  nextTick(() => {
    reloadChart(); // 首次加载
    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时刷新
  });
});

// 清理资源
onBeforeUnmount(() => {
  if (reloadTimer) clearInterval(reloadTimer);
});

onUnmounted(() => {
  if (chartInstance) chartInstance.dispose();
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style scoped>
.w-full { width: 100%; }
.h-full { height: 130px; }
</style>