<template>
  <div class="page">
    <div class="warning-view">
      <div class="scroll-view" ref="scrollViewRef" @mouseenter="onMouseenter" @mouseleave="onMouseleave">
        <div ref="listRef" class="list">
          <div class="item" v-for="(item, index) in data" :key="index">
            <div class="s_title">
              <div class="s_title_left">
                <h2>{{ item.taskType }}</h2>
              </div>
              <div class="s_title_right">
                <span v-if="item.status == '执行中'" style="color: #ce555e">执行中</span>
                <span v-if="item.status == '已完成'" style="color: #ada5fb">已完成</span>
              </div>
            </div>
            <div class="titlebox">任务主体：{{ item.entityName }}</div>
            <ul class="listNr">
              <li>执行者：{{ item.executor }}</li>
              <li>时间：{{ item.taskTime }}</li>
            </ul>
          </div>
        </div>
        <!-- 复制一份列表用于无缝滚动 -->
        <div ref="listRef" class="list">
          <div class="item" v-for="(item, index) in data" :key="'copy-' + index">
            <div class="s_title">
              <div class="s_title_left">
                <h2>{{ item.taskType }}</h2>
              </div>
              <div class="s_title_right">
                <span v-if="item.status == '执行中'" style="color: #ce555e">执行中</span>
                <span v-if="item.status == '已完成'" style="color: #ada5fb">已完成</span>
              </div>
            </div>
            <div class="titlebox">任务主体：{{ item.entityName }}</div>
            <ul class="listNr">
              <li>执行者：{{ item.executor }}</li>
              <li>时间：{{ item.taskTime }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';

const data = ref([]);
const listRef = ref();
const scrollViewRef = ref();
let intervalId = null;
let isAutoScrolling = true;
const scrollSpeed = 1;
const scrollInterval = 50;

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});

// 监听数据变化
watch(
  () => props.config.data,
  (newVal) => {
    if (newVal) {
      data.value = newVal;
      nextTick(() => {
        startScroll();
      });
    }
  },
  { deep: true, immediate: true }
);

const startScroll = () => {
  if (intervalId) {
    clearInterval(intervalId);
  }

  // 确保DOM已经更新
  nextTick(() => {
    if (!scrollViewRef.value || !listRef.value) return;

    intervalId = setInterval(() => {
      if (!isAutoScrolling) return;

      const scrollTop = scrollViewRef.value.scrollTop;
      const firstListHeight = listRef.value.clientHeight;

      if (scrollTop >= firstListHeight) {
        // 滚动到第一个列表的高度时，平滑回到顶部
        scrollViewRef.value.scrollTop = 0;
      } else {
        scrollViewRef.value.scrollTop += scrollSpeed;
      }
    }, scrollInterval);
  });
};

onMounted(() => {
  nextTick(() => {
    startScroll();
  });
});

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
});

const onMouseenter = () => {
  isAutoScrolling = false;
};

const onMouseleave = () => {
  isAutoScrolling = true;
};
</script>

<style scoped>
.page {
  height: 100%;
}

.warning-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  flex: 1;
  height: 0;
  width: 100%;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scroll-view::-webkit-scrollbar {
  display: none;
}

.list {
  width: 100%;
  box-sizing: border-box;
}

.item {
  width: 100%;
  height: 100px;
  min-height: 100px;
  background: url('@/assets/images/s_bg.png');
  background-size: 100% 100%;
  margin-bottom: 10px;
  cursor: pointer;
}

.s_title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding: 10px 15px 0 15px;
}

.s_title_left {
  height: 30px;
  line-height: 30px;
  display: flex;
  justify-content: flex-start;
  h2 {
    line-height: 30px;
    margin: 0;
    color: #e9cc10;
    font-size: 14px;
    font-weight: normal;
  }
}

.s_title_right {
  span {
    line-height: 30px;
    font-size: 14px;
  }
}

.titlebox {
  font-size: 14px;
  color: #74d5ff;
  padding: 0 20px 0 20px;
}

.listNr {
  padding: 5px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  li {
    width: 48%;
    color: #74d5ff;
    font-size: 12px;
  }
}
</style>
