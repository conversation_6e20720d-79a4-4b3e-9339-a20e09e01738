<template>
  <div class="multi-ring-progress-row">
    <svg :width="size" :height="size">
      <g v-for="(item, idx) in rings" :key="idx">
        <!-- 灰色底环 -->
        <circle
          :cx="center"
          :cy="center"
          :r="baseRadius + ((rings.length - 1) * (ringWidth + gap)) / 2 - idx * (ringWidth + gap)"
          stroke="#e0e0e0"
          :stroke-width="ringWidth"
          fill="none"
          :transform="`rotate(-90 ${center} ${center})`"
        />
        <!-- 彩色进度环 -->
        <circle
          :cx="center"
          :cy="center"
          :r="baseRadius + ((rings.length - 1) * (ringWidth + gap)) / 2 - idx * (ringWidth + gap)"
          :stroke="colors[idx]"
          :stroke-width="ringWidth"
          fill="none"
          :stroke-dasharray="
            2 * Math.PI * (baseRadius + ((rings.length - 1) * (ringWidth + gap)) / 2 - idx * (ringWidth + gap))
          "
          :stroke-dashoffset="
            2 *
            Math.PI *
            (baseRadius + ((rings.length - 1) * (ringWidth + gap)) / 2 - idx * (ringWidth + gap)) *
            (1 - item.value)
          "
          stroke-linecap="round"
          style="transition: stroke-dashoffset 1s cubic-bezier(0.4, 2, 0.6, 1)"
          :transform="`rotate(-90 ${center} ${center})`"
        />
      </g>
    </svg>
    <div class="labels-vertical">
      <div v-for="(item, idx) in rings" :key="idx" class="label" :style="{ color: colors[idx] }">
        {{ item.name }}：{{ Math.round(item.value * 100) }}%
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
const props = defineProps({
  config: {
    type: Object,
    data: {}
  }
});
const size = 130;
const ringWidth = 8;
const gap = 4;
const center = size / 2;
const rings = ref([]);
const baseRadius = computed(() => {
  return center - ringWidth / 2 - ((rings.value.length - 1) * (ringWidth + gap)) / 2;
});
const colors = ['#19d9fe', '#1978e6', '#20bc8e', '#e9cc10', '#f2637b', '#975fe5', '#ff8c1a']; // 新增一个橙色
// 监听数据
watch(
  () => props.config.data,
  (newVal) => {
    rings.value = newVal;
  },
  { deep: true }
);
onMounted(() => {
  rings.value = props.config.data;
});
</script>

<style scoped>
.multi-ring-progress-row {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.labels-vertical {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20px;
  margin-left: 40px;
  min-height: 180px;
}
.label {
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(25, 217, 254, 0.18), 0 0px 2px #222;
  line-height: 1.2;
  display: flex;
  align-items: center;
}
.label::after {
  font-size: 16px;
  font-weight: normal;
  margin-left: 6px;
  color: #74d5ff;
}
svg {
  display: block;
}
/* 圆环 hover 高亮 */
svg circle:hover {
  filter: drop-shadow(0 0 2px #19d9fe) brightness(1.2);
  cursor: pointer;
}
</style>
