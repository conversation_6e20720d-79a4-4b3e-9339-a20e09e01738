<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

const initChart = () => {
  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartContainer.value);
  let nameList = props.config.data.nameList;
  let valueList = props.config.data.valueList;

  // 图表数据

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      // 提示框也同步添加单位，保持一致性
      formatter: '{b}: {c} 公顷'
    },
    grid: {
      left: '20%',
      right: '10%',
      top: '10%',
      bottom: '10%'
    },
    yAxis: {
      // yAxis显示分类
      type: 'category',
      data: nameList,
      axisLine: {
        lineStyle: { color: 'rgba(66, 192, 255, .3)' }
      },
      axisLabel: {
        color: '#05D5FF',
        fontSize: 12
      },
      axisTick: { show: false }
    },
    xAxis: {
      // xAxis显示数值（添加公顷单位）
      type: 'value',
      axisLabel: {
        // 关键修改：在数值后添加“公顷”
        formatter: '{value} 公顷',
        color: '#74D5FF',
        fontSize: 12
      },
      axisTick: { show: false },
      axisLine: {
        lineStyle: { color: 'rgba(66, 192, 255, .3)' }
      },
      splitLine: {
        lineStyle: { color: 'rgba(66, 192, 255, .3)' }
      }
    },
    series: [
      {
        type: 'bar',
        data: valueList,
           label: {
        show: true, // 开启显示数值标签
        position: 'right', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
        textStyle: {
          color: '#74D5FF', // 标签文字颜色，可按需调整
          fontSize: 12 // 标签文字大小，可按需调整
        }
      },
        barWidth: 15,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            1,
            0,
            0,
            0, // 横向渐变
            [
              { offset: 0, color: 'rgba(5, 213, 255, 1)' },
              { offset: 0.98, color: 'rgba(5, 213, 255, 0)' }
            ]
          ),
          shadowColor: '#74D5FF',
          shadowBlur: 4
        }
      }
    ]
  };

  chartInstance.setOption(option);
};

// 保留原有的resize和生命周期方法
const resizeChart = () => chartInstance?.resize();
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  resizeChart();
  window.addEventListener('resize', handleResize);
  reloadTimer = setInterval(initChart, 8000);
});

onBeforeUnmount(() => clearInterval(reloadTimer));
onUnmounted(() => {
  chartInstance?.dispose();
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
