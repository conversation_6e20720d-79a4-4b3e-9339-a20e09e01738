<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    YData: [],
    XData: []
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  const data = props.config.data || {};
  const XData = data.XData || [];
  const YData = data.YData || [];
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  const option = {
    color: ['#19d9fe', '#1978e6', '#20bc8e', '#e9cc10', '#a24747', '#6b47a2'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: (params) => {
        return `${params[0].name}: ${params[0].value}万株`;
      }
    },

    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%', // 适当增加底部距离，避免标签被截断
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: XData,
      axisLine: {
        lineStyle: {
          color: '#74D5FF'
        }
      },
      axisLabel: {
        color: '#05D5FF',
        textStyle: {
          fontSize: 12
        },
        interval: 0, // 关键配置：强制显示所有标签（0表示全部显示）
        // 可选：如果标签过挤，可设置旋转角度
        rotate: 0 // 0-90度，根据需要调整（例如rotate: 30）
      },
      axisTick: {
        // 刻度
        show: false
      }
    },
    yAxis: [
      {
        name: '单位:万株',
        nameTextStyle: {
          color: '#74D5FF',
          fontSize: 12,
          align: 'right'
        },

        axisTick: {
          show: false
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#3eb2e8'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#3eb2e8'
          }
        }
      }
    ],
    series: [
      {
        name: '年产量',
        type: 'line',
        data: YData,
        smooth: 0.5,
        symbol: 'circle'
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 10000);
};

onMounted(() => {
  resizeChart();
  initChart();
  window.addEventListener('resize', handleResize);
  startReloadTimer(); // 启动定时器
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
