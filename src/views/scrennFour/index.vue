<template>
  <div class="pictureImg">
    <!-- 按钮 -->
    <div class="floatingBtn float-animation" @click="openChatPanel">
      <img src="@/assets/images/Floating.gif" />
      <h2>参管家</h2>
    </div>
    <!-- 按钮 -->
    <!-- 聊天面板 -->
    <ChatPanel ref="chatPanel" :config="chatConfig" :showTriggerButton="false" />
    <!-- 标题 -->
    <ScrennHeader />
    <!-- 标题 -->
    <!-- 内容展示 -->
    <div class="viewport">
      <div class="columnLeft">
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>林下参种植分布情况</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <BarChart2 :config="chartData.barChart" />
          </div>
        </div>
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>林下参主体种植排名</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <sort-table
              :style="{ height: tableHeight, transition: 'height 0.3s ease-in-out' }"
              :data="dataList01"
              :scroll-delay="30"
              @custom-event="handleRowClick"
            >
              <el-table-column label="排名" align="center" width="50">
                <template v-slot="scope">
                  <span v-if="scope.row.index == 1">
                    <img src="@/assets/images/one.png" height="20px" />
                  </span>
                  <span v-else-if="scope.row.index == 2">
                    <img src="@/assets/images/two.png" height="20px" />
                  </span>
                  <span v-else-if="scope.row.index == 3">
                    <img src="@/assets/images/three.png" height="20px" />
                  </span>
                  <span v-else>
                    {{ scope.row.index }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="地域" align="center" width="130">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.enterprise_name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="种植面积(公顷)" align="center" width="120">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.total_area }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="人参保有量(万株)" align="center">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.total_byl }}</span>
                </template>
              </el-table-column>
            </sort-table>
          </div>
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>种源种植面积比例</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox" style="height: 180px" :style="{ marginTop: viewportHeight === 1080 ? '30px' : '0' }">
            <CircularChart :config="chartData.circularChart" />
          </div>
        </div>
      </div>
      <div class="columnCenter">
        <div class="columnCenterTop" style="height: 66.5%">
          <div class="scrennFourStyle">
            <ul class="scrennFourData">
              <li class="item blue">
                <div class="labels1">种植主体</div>

                <div class="num-unit">
                  <span v-for="(d, i) in String(topData.plantCount)" :key="'plantCount' + i" class="digit-box">{{
                    d
                  }}</span>
                  <span class="unit">家</span>
                </div>
              </li>
              <li class="item cyan">
                <div class="labels2">种植人员</div>

                <div class="num-unit">
                  <span v-for="(d, i) in String(topData.userCount)" :key="'userCount' + i" class="digit-box">{{
                    d
                  }}</span>
                  <span class="unit">人</span>
                </div>
              </li>
              <li class="item yellow">
                <div class="labels3">人参保有量</div>

                <div class="num-unit">
                  <span v-for="(d, i) in String(topData.byl)" :key="'byl' + i" class="digit-box">{{ d }}</span>
                  <span class="unit">亿株</span>
                </div>
              </li>
              <li class="item red">
                <div class="labels4">实际种植面积</div>

                <div class="num-unit">
                  <span v-for="(d, i) in String(topData.area)" :key="'area' + i" class="digit-box">{{ d }}</span>
                  <span class="unit">公顷</span>
                </div>
              </li>
            </ul>
            <div class="scrennFourEchart">
              <div class="map-container">
                <div class="biaoti">舒兰市野山参种植基地分布</div>
                <ul class="biaotixx">
                  <li>
                    <img src="/src/assets/images/dt01.png" />
                    <p>企业基地</p>
                  </li>
                  <li>
                    <img src="/src/assets/images/dt02.png" />
                    <p>个人基地</p>
                  </li>
                </ul>
                <div id="allmap" class="map"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="columnCenterBottom">
          <div class="centerItem">
            <div class="boxTitlex">
              <div class="Title">
                <h2><i></i>年度人参产量情况</h2>
                <ul class="tablist">
                  <li
                    v-for="(tab, index) in tabs"
                    :key="index"
                    @click="clickactiveIndex(index)"
                    :class="{ active: activeIndex === index }"
                  >
                    {{ tab }}
                  </li>
                </ul>
              </div>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <LineChart :config="chartData.lineChart" />
            </div>
          </div>
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>林下参成活率统计</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <ManyChart :config="chartData.manyChart" />
            </div>
          </div>
        </div>
      </div>
      <div class="columnRight">
        <div class="columnItem" style="height: 66.5%; background: transparent">
          <div style="height: 40%">
            <div class="byrenwu">
              <div class="byrenwLeft" :class="viewportHeight === 1080 ? 'className2' : ''">
                <h2>本月任务</h2>
                <h3>{{ totalNum }}</h3>
                <p>任务数量</p>
              </div>
              <ul class="byrenwRight" :class="viewportHeight === 1080 ? 'className2' : ''">
                <li v-for="(item, index) in byrwList">
                  {{ item.name }}<span>{{ item.value }}</span>
                </li>
              </ul>
            </div>
            <div class="selectInput">
              <el-input v-model="input3" style="max-width: 100%; margin-top: 2%" class="input-with-select">
                <template #prepend>
                  <el-select
                    @change="changeSelect"
                    v-model="selectval"
                    placeholder="任务类型"
                    style="width: 115px"
                    clearable
                  >
                    <el-option label="选地" value="选地" />
                    <el-option label="播种" value="播种" />
                    <el-option label="收参" value="收参" />
                    <el-option label="除草" value="除草" />
                    <el-option label="收种" value="收种" />
                    <el-option label="巡地" value="巡地" />
                  </el-select>
                </template>
                <template #append>
                  <el-button :icon="Search" @click="changeSelect" />
                </template>
              </el-input>
            </div>
          </div>
          <div style="height: 57%; margin-top: 2%">
            <ScrollList :config="chartData.scrollList" />
          </div>
        </div>

        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>历史任务完成率统计</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <ul class="historyList">
              <li style="width: 100%; display: flex; justify-content: flex-start">
                <div :style="{ marginTop: viewportHeight === 1080 ? '20px' : '0' }" style="width: 70%">
                  <!-- <statisticsChart :config="chartData.statisticsChart" /> -->
                  <Board02 :config="chartData.statisticsChart" />
                </div>
                <div :style="{ marginTop: viewportHeight === 1080 ? '80px' : '65px' }">
                  <h2 style="font-size: 20px">
                    <span style="color: #38aea1">{{ tj.countList[0] }}</span
                    ><span style="font-size: 14px; margin-left: 5px; color: #cfcae8">项</span>
                  </h2>
                  <h3 style="margin-top: 10px; font-size: 20px; color: #cfcae8">{{ tj.nameList[0] }}</h3>
                </div>
              </li>
              <!-- <li>
                <div :style="{ marginTop: viewportHeight === 1080 ? '20px' : '0' }">
                    <Board02 :config="chartData.statisticsChart"/>
                </div>
                <h2>
                  <span style="color: #16ceb9">{{ tj.countList[1] }}</span
                  ><span style="font-size: 12px; margin-left: 5px;">项</span>
                </h2>
                <h3>{{ tj.nameList[1] }}</h3>
              </li> -->
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- 内容展示 -->
    <!-- 弹框 -->
    <el-dialog v-model="dialogVisible" title="林下参主体种植排名详情" width="600" class="blue-dialog">
      <ul class="visibleBox">
        <li>
          <h2>区域</h2>
          <h3>{{ visibleNr.enterprise_name }}</h3>
        </li>
        <li>
          <h2>种植面积（公顷）</h2>
          <h3>{{ visibleNr.total_area }}</h3>
        </li>
        <li>
          <h2>人参保有量（万株）</h2>
          <h3>{{ visibleNr.total_byl }}</h3>
        </li>
      </ul>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" class="sureBtn">确定</el-button>
          <el-button type="primary" @click="dialogVisible = false" class="resetBtn">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹框 -->
  </div>
  <!-- 自定义地图弹窗 -->
  <div v-if="showCustomInfoWindow" class="customInfoWindow" :style="customInfoWindowStyle">
    <div class="customInfoContent">
      <div class="customInfoHeader">
        <h3>{{ selectedPoint?.name }}</h3>
        <button class="closeBtn" @click="closeCustomInfoWindow">×</button>
      </div>
      <div class="customInfoBody">
        <hr />
        <div class="infoGrid">
          <div class="infoItem">
            <span class="label">经营类型:</span>
            <span class="value">{{ selectedPoint?.type === 0 ? '企业基地' : '个人基地' }}</span>
          </div>
          <div class="infoItem">
            <span class="label">宜参林地:</span>
            <span class="value">{{ selectedPoint?.ysld || '暂无' }}公顷</span>
          </div>
          <div class="infoItem">
            <span class="label">宜参耕地:</span>
            <span class="value">{{ selectedPoint?.ysgd || '暂无' }}公顷</span>
          </div>
          <div class="infoItem">
            <span class="label">林地已种面积:</span>
            <span class="value">{{ selectedPoint?.ldyzmj || '暂无' }}公顷</span>
          </div>
          <div class="infoItem">
            <span class="label">耕地已种面积:</span>
            <span class="value">{{ selectedPoint?.gdyzmj || '暂无' }}公顷</span>
          </div>
          <div class="infoItem">
            <span class="label">信用等级:</span>
            <span class="value">{{ selectedPoint?.credit_level || '暂无' }}</span>
          </div>
          <div class="infoItem">
            <span class="label">总面积:</span>
            <span class="value">{{ selectedPoint?.mj || '暂无' }}公顷</span>
          </div>
          <div class="infoItem">
            <span class="label">归属区域:</span>
            <span class="value">{{ selectedPoint?.addr || '暂无' }}</span>
          </div>
          <div class="infoItem">
            <span class="label">负责人:</span>
            <span class="value">{{ selectedPoint?.leader || '暂无' }}</span>
          </div>
          <div class="infoItem">
            <span class="label">联系电话:</span>
            <span class="value">{{ formatPhone(selectedPoint?.tel) || '暂无' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, onBeforeUnmount, nextTick } from 'vue';
import ScrennHeader from '@/components/ScrennHeader';
import BarChart2 from './components/BarChart2.vue';
import statisticsChart from './components/statisticsChart.vue';
import Board01 from './components/Board01.vue';
import Board02 from './components/Board02.vue';
import SortTable from '@/components/SortTable';
import CircularChart from './components/CircularChart.vue';
import LineChart from './components/LineChart.vue';
import ManyChart from './components/ManyChart.vue';
import statisticsChart2 from './components/statisticsChar2.vue';
import ScrollList from './components/ScrollList.vue';
import { Search } from '@element-plus/icons-vue';
import ChatPanel from '../../components/ChatPanel/index.vue';
import {
  getPlantingDistribution,
  getPlantingRanking,
  getPlantingAreaRatio,
  getTopStatistics,
  getAnnualOutput,
  getSurvivalRate,
  getMonthlyTasks,
  getHistoricalTaskCompletion,
  getTaskList,
  getMapData
} from '@/api/screen/index4';

//页面高度
const viewportHeight = ref(0);
const updateViewportHeight = () => {
  viewportHeight.value = window.innerHeight;
};
// 自定义弹窗相关
const showCustomInfoWindow = ref(false);
const selectedPoint = ref(null);
const customInfoWindowStyle = ref({
  left: '0px',
  top: '0px'
});

// 弹框样式
const dialogVisible = ref(false);
//弹框内容
const visibleNr = ref({});
// 监听表格单元格点击（Element Plus 原生事件）
const handleRowClick = (row) => {
  dialogVisible.value = true;
  visibleNr.value = row;
};

// 添加表格高度响应式变量
const tableHeight = ref('100%');

//搜索
const input3 = ref('');
const selectval = ref('');
const activeIndex = ref(0);
//tab
const tabs = ref(['林下参', '园参']);
const topData = ref({
  area: 0,
  byl: 0,
  plantCount: 0,
  userCount: 0
});
const closeCustomInfoWindow = () => {
  showCustomInfoWindow.value = false;
  selectedPoint.value = null;
};
//数据
const dataList01 = ref([]);
const clickactiveIndex = async (index) => {
  activeIndex.value = index;
  const res4 = await getAnnualOutput();
  if (res4.code === 200) {
    if (index === 0) {
      chartData.lineChart.data.XData = res4.data.yearList;
      chartData.lineChart.data.YData = res4.data.linxiashenList;
    } else {
      chartData.lineChart.data.XData = res4.data.yearList;
      chartData.lineChart.data.YData = res4.data.yuanshenList;
    }
  }
};
// ChatPanel 配置
const chatConfig = ref({
  ip: '************',
  title: '参管家',
  wsPort: 8282,
  httpPort: 8383,
  showTriggerButton: false
});
// 各个图表的数据
const chartData = reactive({
  // 主体数量统计
  barChart: {
    data: []
  },
  circularChart: {
    data: []
  },
  lineChart: {
    data: {
      YData: [],
      XData: []
    }
  },
  manyChart: {
    data: {}
  },
  scrollList: {
    data: []
  },
  statisticsChart: {
    data: {
      value: 0,
      total: 0
    }
  }
});
const byrwList = ref([]);
const totalNum = ref(0);

//地图数据
//舒兰市关键坐标点数据
const shulanPoints = ref([]);
const labelList = ref([]);
//地图数据
// 获取组件引用
const chatPanel = ref(null);

// 打开面板方法
const openChatPanel = () => {
  chatPanel.value?.openPanel();
};
const tj = ref({
  countList: [0, 0],
  nameList: ['', ''],
  valueList: [0, 0]
});
onMounted(() => {
  getdata();
  updateViewportHeight(); // 初始化视口高度
  window.addEventListener('resize', updateViewportHeight); // 监听窗口大小变化
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateViewportHeight); // 移除监听
});
const changeSelect = () => {
  let data = {
    taskType: selectval.value,
    keyword: input3.value
  };
  getTaskList(data).then((res) => {
    if (res.code === 200) {
      chartData.scrollList.data = res.data;
    }
  });
};
const getdata = async () => {
  const res = await getPlantingDistribution();
  if (res.code === 200) {
    chartData.barChart.data = res.data;
  }
  const res2 = await getPlantingRanking();
  if (res2.code === 200) {
    // 添加高度动画效果
    tableHeight.value = '99%';
    dataList01.value = res2.data;
  }
  const res3 = await getPlantingAreaRatio();
  if (res3.code === 200) {
    const formattedData = res3.data.nameList.map((name, index) => {
      return {
        name: name,
        value: res3.data.valueList[index]
      };
    });
    chartData.circularChart.data = formattedData;
  }
  const res4 = await getAnnualOutput();
  if (res4.code === 200) {
    chartData.lineChart.data.XData = res4.data.yearList;
    chartData.lineChart.data.YData = res4.data.linxiashenList;
  }

  const res5 = await getSurvivalRate();
  // nameList: (4)[('0-5年参', '5-10年参', '10-15年参', '15年以上参')];
  // valueList: (4)[(65, 70, 80, 83)];
  if (res5.code === 200) {
    const formattedData = res5.data.nameList.map((name, index) => {
      return {
        name: name,
        value: res5.data.valueList[index] / 100
      };
    });
    chartData.manyChart.data = formattedData;
  }
  const res6 = await getMonthlyTasks();
  if (res6.code === 200) {
    byrwList.value = res6.data.list;
    totalNum.value = res6.data.total;
  }
  const res7 = await getHistoricalTaskCompletion();
  if (res7.code === 200) {
    chartData.statisticsChart.data = {
      value: res7.data.valueList[0],
      value2: res7.data.valueList[1]
    };
    tj.value = res7.data;
  }
  const res8 = await getTaskList();
  if (res8.code === 200) {
    chartData.scrollList.data = res8.data;
  }

  const res9 = await getTopStatistics();
  if (res9.code === 200) {
    topData.value = res9.data;
    //保留两位小数
    let num = res9.data.byl / 1;
    topData.value.byl = num.toFixed(2);
  }

  const res10 = await getMapData();
  shulanPoints.value = res10.data;
  // addImageMarkers();
};

// 地图实例
const map = ref(null);
// 边界多边形实例
const boundaryPolygon = ref(null);
// 坐标点覆盖物数组
const pointMarkers = ref([]);

// 图片图标配置（根据点类型设置不同图片）
const iconConfig = {
  0: {
    url: new URL('/src/assets/images/dt01.png', import.meta.url).href, // 市中心点图片路径
    width: 30,
    height: 30
  },
  1: {
    url: new URL('/src/assets/images/dt02.png', import.meta.url).href, // 乡镇点图片路径
    width: 30,
    height: 30
  }
};

// 加载百度地图API
const loadBMapScript = () => {
  return new Promise((resolve, reject) => {
    if (window.BMapGL) {
      resolve();
      return;
    }
    const script = document.createElement('script');
    script.onload = () => resolve();
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

// 创建图片图标
const createImageIcon = (type) => {
  const config = iconConfig[type];
  if (!config) {
    console.error(`未找到类型为${type}的图标配置`);
    return null;
  }

  return new window.BMapGL.Icon(config.url, new window.BMapGL.Size(config.width, config.height), {
    anchor: new window.BMapGL.Size(config.width / 2, config.height / 2),
    imageOffset: new window.BMapGL.Size(0, 0)
  });
};

// 添加图片标记点
const addImageMarkers = () => {
  if (!map.value) return;

  // 清空现有标记
  clearPointMarkers();

  // 5. 监听zoomend事件，动态控制label显示/隐藏
  map.value.addEventListener('zoomend', function () {
    var currentZoom = map.value.getZoom();
    if (currentZoom >= 17) {
      //所有的label都显示
      labelList.value.forEach((label) => {
        label.show();
      });
    } else {
      //所有的label都隐藏
      labelList.value.forEach((label) => {
        label.hide();
      });
    }
  });
  // 添加新标记
  shulanPoints.value.forEach((point) => {
    // 1. 创建百度地图坐标点（注意是逗号分隔经纬度！）
    const markerPoint = new window.BMapGL.Point(point.lng, point.lat);

    // 2. 创建图标（你原来的 createImageIcon 逻辑，这里假设能正常返回 Icon）
    const icon = createImageIcon(point.type);

    // 3. 创建标记 & 信息窗口
    if (icon) {
      const marker = new window.BMapGL.Marker(markerPoint, { icon });

      // 4. 绑定点击事件，打开自定义弹窗
      marker.addEventListener('click', () => {
        // 计算弹窗位置（将地图坐标转换为屏幕坐标）
        const pixel = map.value.pointToPixel(markerPoint);
        const mapContainer = document.getElementById('allmap');
        const rect = mapContainer.getBoundingClientRect();

        // 设置弹窗位置
        customInfoWindowStyle.value = {
          left: `${pixel.x + rect.left}px`,
          top: `${pixel.y + rect.top - 200}px` // 向上偏移200px
        };

        // 显示自定义弹窗
        selectedPoint.value = point;
        showCustomInfoWindow.value = true;
      });

      // 5. 把标记添加到地图
      map.value.addOverlay(marker);
      pointMarkers.value.push(marker);
    }
    //4. 增加基地边界
    const boundaryData = point.base_location;
    if (boundaryData) {
      //转化为json
      const boundaryJSON = JSON.parse(boundaryData);
      const pointArray = boundaryJSON.map((p) => new BMapGL.Point(p[0], p[1]));
      var polygon = new window.BMapGL.Polygon(pointArray, {
        strokeColor: 'red',
        strokeWeight: 0.5,
        strokeOpacity: 0.8,
        fillOpacity: 0.5,
        fillColor: 'green'
      });
      map.value.addOverlay(polygon);
      // 计算中心点
      var bounds = polygon.getBounds();
      var center = bounds.getCenter();
      // 创建Label
      var label = new BMapGL.Label(point.name, {
        position: center,
        offset: new BMapGL.Size(-20, -50)
      });
      label.setStyle({
        color: 'red',
        fontSize: '12px',
        padding: '2px',
        border: 'none',
        background: 'none'
      });
      label.hide();
      map.value.addOverlay(label);
      labelList.value.push(label);
    }
  });
};

// 清除标记点
const clearPointMarkers = () => {
  if (!map.value) return;

  pointMarkers.value.forEach((marker) => {
    map.value.removeOverlay(marker);
  });
  pointMarkers.value = [];
};

// 初始化地图
const initMap = async () => {
  await loadBMapScript();
  // 创建地图实例
  map.value = new window.BMapGL.Map('allmap');
  const centerPoint = new window.BMapGL.Point(127.1, 44.4);
  map.value.centerAndZoom(centerPoint, 9.95);
  map.value.enableScrollWheelZoom(true);

  // 设置地图类型（先设置再添加边界）
  map.value.setMapType(BMAP_EARTH_MAP);

  // 获取并绘制舒兰市边界
  const boundary = new window.BMapGL.Boundary();
  boundary.get('舒兰市', function (rs) {
    if (!rs.boundaries || rs.boundaries.length === 0) {
      console.error('未能获取到舒兰市边界数据');
      return;
    }

    // 解析边界点并创建多边形
    const points = rs.boundaries[0].split(';').map((p) => {
      const [lng, lat] = p.split(',');
      return new window.BMapGL.Point(lng, lat);
    });

    // 创建边界多边形
    boundaryPolygon.value = new window.BMapGL.Polygon(points, {
      strokeColor: 'yellow', // 边界线颜色 #7EC8E3
      strokeWeight: 1, // 边界线宽度
      //fillColor: 'rgba(27, 142, 236, 0.9)', // 填充颜色（半透明）
      //fillOpacity: 0.8 // 填充透明度
      fillOpacity: 0
    });

    // 添加边界到地图
    map.value.addOverlay(boundaryPolygon.value);

    // 为边界添加点击事件
    boundaryPolygon.value.addEventListener('click', function () {});
  });
};

// 组件挂载后初始化地图
onMounted(async () => {
  await nextTick();
  initMap().catch((error) => {
    console.error('百度地图加载失败:', error);
  });
});
watch(
  shulanPoints,
  (newVal) => {
    if (newVal.length > 0) {
      clearPointMarkers();
      addImageMarkers();
    }
  },
  { deep: true }
);

// 组件卸载时清理资源
onUnmounted(() => {
  if (map.value) {
    map.value.clearOverlays();
    map.value = null;
  }
  boundaryPolygon.value = null;
  clearPointMarkers();
});

// 电话号码脱敏处理
const formatPhone = (phone) => {
  if (!phone) return '暂无';

  // 移除所有非数字字符
  const digits = phone.replace(/\D/g, '');

  // 根据长度处理不同格式
  if (digits.length === 11) {
    return `${digits.substring(0, 3)}****${digits.substring(7)}`;
  } else {
    // 其他格式，保持原样
    return phone;
  }
};
</script>
<style lang="scss" scoped>
// 自定义弹窗样式
.customInfoWindow {
  position: fixed;
  z-index: 9999;
  background: rgba(20, 24, 38, 0.95);
  color: #e6f7ff;
  border-radius: 12px;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.8);
  border: 1px solid #2a3040;
  width: 350px;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease-out;

  .customInfoContent {
    padding: 16px 20px;

    .customInfoHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h3 {
        color: #40a9ff;
        font-weight: 600;
        font-size: 16px;
        margin: 0;
      }

      .closeBtn {
        background: none;
        border: none;
        color: #90caf9;
        font-size: 20px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s;

        &:hover {
          background: rgba(144, 202, 249, 0.1);
          color: #e6f7ff;
        }
      }
    }

    .customInfoBody {
      font-size: 14px;
      line-height: 1.6;

      hr {
        border: none;
        border-top: 1px solid #2a3040;
        margin: 8px 0 12px 0;
      }

      .infoGrid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 8px;
        overflow-y: auto;

        .infoItem {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 0;
          border-bottom: 1px solid rgba(42, 48, 64, 0.3);

          &:last-child {
            border-bottom: none;
          }

          .label {
            color: #90caf9;
            font-weight: 500;
            font-size: 13px;
            min-width: 80px;
          }

          .value {
            color: #e6f7ff;
            font-weight: 400;
            font-size: 13px;
            text-align: right;
            flex: 1;
            margin-left: 12px;
          }
        }
      }

      .devList {
        // height: 200px;
        overflow-y: auto;

        .devItem {
          padding: 6px 0;
          border-bottom: 1px solid rgba(42, 48, 64, 0.5);

          &:last-child {
            border-bottom: none;
          }

          p {
            margin: 0;
            color: #90caf9;
            font-weight: 500;
          }
        }
      }

      .noData {
        color: #909399;
        text-align: center;
        padding: 20px 0;
        font-style: italic;
      }
    }
  }
}
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map {
  width: 100%;
  height: 100%;
}

.biaoti {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  padding: 10px 20px;
  background-color: #093fa3;
  color: #ffffff;
  border-bottom-left-radius: 14px;
  border-bottom-right-radius: 14px;
}

.biaotixx {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 9;
  background-color: #093fa3;
  padding: 10px 15px 0 15px;
  border-radius: 8px;

  li {
    display: flex;
    margin-bottom: 10px;
    color: #ffffff;

    img {
      width: 20px;
      height: 20px;
      margin-right: 5px;
    }
  }
}

:deep(.el-input__wrapper) {
  background-color: #001033;
}

:deep(.el-input-group__append) {
  background-color: #001033 !important;
  color: #74d5ff;
}

:deep(.el-input-group__prepend) {
  background-color: #001033 !important;
  // box-shadow: 0 1px 0 0 #3164b1 inset,
  //  0 -1px 0 0 #3164b1 inset,
  //   -1px 0 0 0 #3164b1 inset;
}

:deep(.el-input__wrapper) {
  box-shadow:
    0 1px 0 0 #74d5ff inset,
    0 -1px 0 0 #74d5ff inset,
    -1px 0 0 0 #74d5ff inset;
}

:deep(.el-select__placeholder) {
  color: #74d5ff;
}

:deep(.el-input__placeholder) {
  color: #74d5ff;
}

:deep(.el-input-group__append) {
  box-shadow:
    0 1px 0 0 #74d5ff inset,
    0 -1px 0 0 #74d5ff inset,
    -1px 0 0 0 #74d5ff inset;
}

:deep(.el-select__placeholder.is-transparent) {
  color: #74d5ff;
}

:deep(.el-select__caret) {
  color: #74d5ff;
}

:deep(.el-input-group--prepend .el-input-group__prepend .el-select .el-select__wrapper) {
  box-shadow:
    0 1px 0 0 #74d5ff inset,
    0 -1px 0 0 #74d5ff inset,
    -1px 0 0 0 #74d5ff inset;
}

:deep(.el-input__inner) {
  color: #74d5ff !important;
}

.historyList {
  display: flex;
  justify-content: space-between;

  li {
    width: 50%;
    text-align: center;
    margin-top: 2%;

    h2 {
      font-size: 18px;
      color: #fff;
      margin: 0;
      font-weight: normal;
      margin-bottom: 5px;
    }

    h3 {
      font-size: 16px;
      color: #fff;
      margin: 0;
      font-weight: normal;
    }
  }
}

.scrennFourData {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.scrennFourData .item {
  display: flex;
  flex-direction: column;
  // align-items: center;
}
.scrennFourData .num-unit {
  display: flex;
  align-items: flex-end;
  margin-top: 10px;
}
.digit-box {
  display: inline-block;
  min-width: 36px;
  height: 48px;
  margin: 0 2px;
  background: rgba(0, 198, 255, 0.12);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 2px solid currentColor;
  color: inherit;
  font-size: 40px;
  font-family: 'DIN Alternate', Arial, sans-serif;
  font-weight: bold;
  text-align: center;
  line-height: 48px;
  transition: background 0.2s;
}
.unit {
  font-size: 20px;
  margin-left: 8px;
  font-weight: bold;
}
.labels1 {
  font-size: 18px;
  margin-top: 6px;
  letter-spacing: 1px;
  color: #239aff;
}
.labels2 {
  font-size: 18px;
  margin-top: 6px;
  letter-spacing: 1px;
  color: #00ffe7;
}
.labels3 {
  font-size: 18px;
  margin-top: 6px;
  letter-spacing: 1px;
  color: #ffe066;
}
.labels4 {
  font-size: 18px;
  margin-top: 6px;
  letter-spacing: 1px;
  color: #ff6b81;
}

.item.blue .digit-box,
.item.blue .unit {
  color: #239aff;
  border-color: #239aff;
}
.item.cyan .digit-box,
.item.cyan .unit {
  color: #00ffe7;
  border-color: #00ffe7;
}
.item.yellow .digit-box,
.item.yellow .unit {
  color: #ffe066;
  border-color: #ffe066;
}
.item.red .digit-box,
.item.red .unit {
  color: #ff6b81;
  border-color: #ff6b81;
}
.className2 {
  margin-top: 8px;
  li {
    font-size: 13px;
  }
}
</style>
