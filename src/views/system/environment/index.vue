<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="创建时间" prop="createDate">
        <el-date-picker
          clearable
          v-model="queryParams.createDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="基地名称" prop="baseName">
        <el-input v-model="queryParams.baseName" placeholder="请输入基地名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:environment:add']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:environment:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:environment:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="environmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="创建时间" align="center" prop="createDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="PH值" align="center" prop="ph" />
      <el-table-column label="养分含量" align="center" prop="yfhl" />
      <el-table-column label="含盐量" align="center" prop="hyl" />
      <el-table-column label="含碱量" align="center" prop="hjl" />
      <el-table-column label="基地名称" align="center" prop="baseName" />
      <el-table-column label="有机物含量" align="center" prop="yjwhl" />
      <el-table-column label="电导率" align="center" prop="ddl" />
      <el-table-column label="湿度" align="center" prop="kqsd" />
      <el-table-column label="温度" align="center" prop="kqwd" />
      <el-table-column label="土壤湿度" align="center" prop="trsd" />
      <el-table-column label="土壤温度" align="center" prop="trwd" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:environment:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:environment:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改环境管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="environmentRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="创建时间" prop="createDate">
          <el-date-picker
            clearable
            v-model="form.createDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="PH值" prop="ph">
          <el-input v-model="form.ph" placeholder="请输入PH值" />
        </el-form-item>
        <el-form-item label="养分含量" prop="yfhl">
          <el-input v-model="form.yfhl" placeholder="请输入养分含量" />
        </el-form-item>
        <el-form-item label="含盐量" prop="hyl">
          <el-input v-model="form.hyl" placeholder="请输入含盐量" />
        </el-form-item>
        <el-form-item label="含碱量" prop="hjl">
          <el-input v-model="form.hjl" placeholder="请输入含碱量" />
        </el-form-item>
        <el-form-item label="基地名称" prop="baseName">
          <el-input v-model="form.baseName" placeholder="请输入基地名称" />
        </el-form-item>
        <el-form-item label="有机物含量" prop="yjwhl">
          <el-input v-model="form.yjwhl" placeholder="请输入有机物含量" />
        </el-form-item>
        <el-form-item label="电导率" prop="ddl">
          <el-input v-model="form.ddl" placeholder="请输入电导率" />
        </el-form-item>
        <el-form-item label="湿度" prop="kqsd">
          <el-input v-model="form.kqsd" placeholder="请输入湿度" />
        </el-form-item>
        <el-form-item label="温度" prop="kqwd">
          <el-input v-model="form.kqwd" placeholder="请输入温度" />
        </el-form-item>
        <el-form-item label="土壤湿度" prop="trsd">
          <el-input v-model="form.trsd" placeholder="请输入土壤湿度" />
        </el-form-item>
        <el-form-item label="土壤温度" prop="trwd">
          <el-input v-model="form.trwd" placeholder="请输入土壤温度" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Environment">
import {
  listEnvironment,
  getEnvironment,
  delEnvironment,
  addEnvironment,
  updateEnvironment
} from '@/api/system/environment';

const { proxy } = getCurrentInstance();

const environmentList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    createDate: null,
    ph: null,
    yfhl: null,
    hyl: null,
    hjl: null,
    baseName: null,
    yjwhl: null,
    ddl: null,
    kqsd: null,
    kqwd: null,
    trsd: null,
    trwd: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询环境管理列表 */
function getList() {
  loading.value = true;
  listEnvironment(queryParams.value).then((response) => {
    environmentList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    createDate: null,
    baseId: null,
    ph: null,
    yfhl: null,
    hyl: null,
    hjl: null,
    baseName: null,
    deviceId: null,
    yjwhl: null,
    ddl: null,
    kqsd: null,
    kqwd: null,
    trsd: null,
    trwd: null
  };
  proxy.resetForm('environmentRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加环境管理';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getEnvironment(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改环境管理';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['environmentRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateEnvironment(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addEnvironment(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除环境管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delEnvironment(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/environment/export',
    {
      ...queryParams.value
    },
    `environment_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
