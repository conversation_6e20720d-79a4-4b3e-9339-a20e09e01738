<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="主体名称" prop="entityName">
        <el-input v-model="queryParams.entityName" placeholder="请输入主体名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="负责人" prop="leader">
        <el-input v-model="queryParams.leader" placeholder="请输入负责人" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item label="成立日期" prop="establishDate">
        <el-date-picker
          clearable
          v-model="queryParams.establishDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择成立日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="省" prop="provinceCode">
        <el-input v-model="queryParams.provinceCode" placeholder="请输入省" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="市" prop="cityCode">
        <el-input v-model="queryParams.cityCode" placeholder="请输入市" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="县" prop="countyCode">
        <el-input v-model="queryParams.countyCode" placeholder="请输入县" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item label="信用等级" prop="creditLevel">
        <el-select v-model="queryParams.creditLevel" placeholder="请选择信用等级" clearable style="width: 150px">
          <el-option v-for="dict in credit_level" :key="dict.dictCode" :label="dict.dictLabel" :value="dict.dictCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="主体类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择主体类型" clearable style="width: 150px">
          <el-option
            v-for="dict in subject_entity_type"
            :key="dict.dictCode"
            :label="dict.dictLabel"
            :value="dict.dictCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:entity:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:entity:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:entity:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:entity:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="entityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="主体名称" align="center" prop="entityName" />
      <el-table-column label="负责人" align="center" prop="leader" />
      <el-table-column label="电话" align="center" prop="tel" />
      <el-table-column label="成立日期" align="center" prop="establishDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.establishDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" />
      <el-table-column label="省" align="center" prop="provinceCode" />
      <el-table-column label="市" align="center" prop="cityCode" />
      <el-table-column label="县" align="center" prop="countyCode" />
      <el-table-column label="乡" align="center" prop="townsCode" />
      <el-table-column label="村" align="center" prop="villageCode" />
      <el-table-column label="信用等级" align="center" prop="creditLevelName"> </el-table-column>
      <el-table-column label="主体类型" align="center" prop="typeName"> </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template #default="scope">
          <div style="display: flex">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:entity:edit']"
              >修改</el-button
            >
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:entity:remove']"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改销售主体对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="entityRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="主体名称" prop="entityName">
          <el-input v-model="form.entityName" placeholder="请输入主体名称" />
        </el-form-item>
        <el-form-item label="负责人" prop="leader">
          <el-input v-model="form.leader" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="电话" prop="tel">
          <el-input v-model="form.tel" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="成立日期" prop="establishDate">
          <el-date-picker
            clearable
            v-model="form.establishDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择成立日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="省" prop="provinceCode">
          <el-input v-model="form.provinceCode" placeholder="请输入省" />
        </el-form-item>
        <el-form-item label="市" prop="cityCode">
          <el-input v-model="form.cityCode" placeholder="请输入市" />
        </el-form-item>
        <el-form-item label="县" prop="countyCode">
          <el-input v-model="form.countyCode" placeholder="请输入县" />
        </el-form-item>
        <el-form-item label="乡" prop="townsCode">
          <el-input v-model="form.townsCode" placeholder="请输入乡" />
        </el-form-item>
        <el-form-item label="村" prop="villageCode">
          <el-input v-model="form.villageCode" placeholder="请输入村" />
        </el-form-item>
        <el-form-item label="信用等级" prop="creditLevel">
          <el-select v-model="form.creditLevel" placeholder="请选择信用等级" clearable>
            <el-option
              v-for="dict in credit_level"
              :key="dict.dictCode"
              :label="dict.dictLabel"
              :value="dict.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主体类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择主体类型">
            <el-option
              v-for="dict in subject_entity_type"
              :key="dict.dictCode"
              :label="dict.dictLabel"
              :value="dict.dictCode"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Entity">
import { listEntity, getEntity, delEntity, addEntity, updateEntity } from '@/api/system/entity';
import { getDicts } from '@/api/system/dict/data';
const { proxy } = getCurrentInstance();

const entityList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    entityName: null,
    leader: null,
    tel: null,
    establishDate: null,
    longitude: null,
    latitude: null,
    provinceCode: null,
    cityCode: null,
    countyCode: null,
    townsCode: null,
    villageCode: null,
    type: null,
    creditLevel: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);
onMounted(() => {
  handleselect();
});
/** 查询销售主体列表 */
function getList() {
  loading.value = true;
  listEntity(queryParams.value).then((response) => {
    entityList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
const subject_entity_type = ref([]);
const credit_level = ref([]);
const handleselect = async (value) => {
  const res1 = await getDicts('credit_level');
  const res2 = await getDicts('subject_entity_type');
  credit_level.value = res1.data;
  subject_entity_type.value = res2.data;
};

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    createTime: null,
    updateTime: null,
    createUserId: null,
    updateUserId: null,
    entityName: null,
    leader: null,
    tel: null,
    establishDate: null,
    longitude: null,
    latitude: null,
    provinceCode: null,
    cityCode: null,
    countyCode: null,
    townsCode: null,
    villageCode: null,
    type: null,
    creditLevel: null
  };
  proxy.resetForm('entityRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加销售主体';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getEntity(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改销售主体';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['entityRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateEntity(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addEntity(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除销售主体编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delEntity(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/entity/export',
    {
      ...queryParams.value
    },
    `entity_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
