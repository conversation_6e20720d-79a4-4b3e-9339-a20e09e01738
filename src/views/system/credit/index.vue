<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="创建时间" prop="createDate">
        <el-date-picker
          clearable
          v-model="queryParams.createDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="基地ID" prop="baseId">
        <el-input v-model="queryParams.baseId" placeholder="请输入基地ID" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item label="授信状态" prop="creditStatus">
        <el-select v-model="queryParams.creditStatus" placeholder="请选择授信状态" clearable style="width: 150px">
          <el-option v-for="dict in task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="基地名称" prop="baseName">
        <el-input v-model="queryParams.baseName" placeholder="请输入基地名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="金融名称" prop="creditName">
        <el-input v-model="queryParams.creditName" placeholder="请输入金融名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:credit:add']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:credit:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:credit:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="creditList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="创建时间" align="center" prop="createDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="基地ID" align="center" prop="baseId" />
      <el-table-column label="授信额度" align="center" prop="creditCount" />
      <el-table-column label="授信状态" align="center" prop="creditStatus">
        <template #default="scope">
          <dict-tag :options="task_status" :value="scope.row.creditStatus" />
        </template>
      </el-table-column>
      <el-table-column label="基地名称" align="center" prop="baseName" />
      <el-table-column label="金融名称" align="center" prop="creditName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:credit:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:credit:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改授信对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="creditRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="创建时间" prop="createDate">
          <el-date-picker
            clearable
            v-model="form.createDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="基地ID" prop="baseId">
          <el-input v-model="form.baseId" placeholder="请输入基地ID" />
        </el-form-item>
        <el-form-item label="授信额度" prop="creditCount">
          <el-input v-model="form.creditCount" placeholder="请输入授信额度" />
        </el-form-item>
        <el-form-item label="授信状态" prop="creditStatus">
          <el-select v-model="form.creditStatus" placeholder="请选择授信状态">
            <el-option
              v-for="dict in task_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="基地名称" prop="baseName">
          <el-input v-model="form.baseName" placeholder="请输入基地名称" />
        </el-form-item>
        <el-form-item label="金融名称" prop="creditName">
          <el-input v-model="form.creditName" placeholder="请输入金融名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Credit">
import { listCredit, getCredit, delCredit, addCredit, updateCredit } from '@/api/system/credit';

const { proxy } = getCurrentInstance();
const { task_status } = proxy.useDict('task_status');

const creditList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    createDate: null,
    baseId: null,
    creditCount: null,
    creditStatus: null,
    baseName: null,
    creditName: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询授信列表 */
function getList() {
  loading.value = true;
  listCredit(queryParams.value).then((response) => {
    creditList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    createDate: null,
    baseId: null,
    creditCount: null,
    creditStatus: null,
    baseName: null,
    creditName: null
  };
  proxy.resetForm('creditRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加授信';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getCredit(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改授信';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['creditRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateCredit(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addCredit(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除授信编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delCredit(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/credit/export',
    {
      ...queryParams.value
    },
    `credit_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
