<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="创建时间" prop="create_date">
        <el-date-picker
          clearable
          v-model="queryParams.create_date"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="基地名称" prop="baseName">
        <el-input v-model="queryParams.baseName" placeholder="请输入基地名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="任务名称" prop="jobName">
        <el-input v-model="queryParams.jobName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="任务类型" prop="jobType">
        <el-select v-model="queryParams.jobType" placeholder="请选择任务类型" clearable style="width: 150px">
          <el-option v-for="dict in job_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="执行人" prop="jobExecuter">
        <el-input v-model="queryParams.jobExecuter" placeholder="请输入执行人" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="执行结果" prop="jobStatus">
        <el-select v-model="queryParams.jobStatus" placeholder="请选择执行结果" clearable style="width: 150px">
          <el-option v-for="dict in task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:job:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:job:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:job:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:job:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="创建时间" align="center" prop="create_date" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.create_date, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="基地名称" align="center" prop="baseName" />
      <el-table-column label="任务名称" align="center" prop="jobName" />
      <el-table-column label="任务类型" align="center" prop="jobType">
        <template #default="scope">
          <dict-tag :options="job_type" :value="scope.row.jobType" />
        </template>
      </el-table-column>
      <el-table-column label="执行人" align="center" prop="jobExecuter" />
      <el-table-column label="执行结果" align="center" prop="jobStatus">
        <template #default="scope">
          <dict-tag :options="task_status" :value="scope.row.jobStatus" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:job:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:job:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改种植标准任务管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="jobRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="创建时间" prop="create_date">
          <el-date-picker
            clearable
            v-model="form.create_date"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="基地名称" prop="baseName">
          <el-input v-model="form.baseName" placeholder="请输入基地名称" />
        </el-form-item>
        <el-form-item label="任务名称" prop="jobName">
          <el-input v-model="form.jobName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务类型" prop="jobType">
          <el-select v-model="form.jobType" placeholder="请选择任务类型">
            <el-option v-for="dict in job_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行人" prop="jobExecuter">
          <el-input v-model="form.jobExecuter" placeholder="请输入执行人" />
        </el-form-item>
        <el-form-item label="执行结果" prop="jobStatus">
          <el-select v-model="form.jobStatus" placeholder="请选择执行结果">
            <el-option
              v-for="dict in task_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Job">
import { listJob, getJob, delJob, addJob, updateJob } from '@/api/system/job';

const { proxy } = getCurrentInstance();
const { task_status, job_type } = proxy.useDict('task_status', 'job_type');

const jobList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    create_date: null,
    baseName: null,
    jobName: null,
    jobType: null,
    jobExecuter: null,
    jobStatus: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询种植标准任务管理列表 */
function getList() {
  loading.value = true;
  listJob(queryParams.value).then((response) => {
    jobList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    create_date: null,
    baseId: null,
    baseName: null,
    jobName: null,
    jobType: null,
    jobExecuter: null,
    jobStatus: null
  };
  proxy.resetForm('jobRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加种植标准任务管理';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getJob(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改种植标准任务管理';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['jobRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateJob(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addJob(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除种植标准任务管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delJob(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/job/export',
    {
      ...queryParams.value
    },
    `job_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
