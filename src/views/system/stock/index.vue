<!--suppress ALL -->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="加工厂名称" prop="factoryName">
        <el-select v-model="queryParams.factoryName" placeholder="请选择加工厂名称" clearable style="width: 200px">
          <el-option v-for="item in factoryList" :key="item.id" :label="item.factoryName" :value="item.factoryName" />
        </el-select>
      </el-form-item>
      <el-form-item label="成品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入成品名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createDate">
        <el-date-picker
          clearable
          v-model="queryParams.createDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:stock:add']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:stock:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:stock:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stockList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="选后" align="center" prop="id" />
      <el-table-column label="加工厂名称" align="center" prop="factoryName" />
      <el-table-column label="成品名称" align="center" prop="productName" />
      <el-table-column label="创建时间" align="center" prop="createDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库存量" align="center" prop="amount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:stock:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:stock:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改加工成品库存管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="stockRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="加工厂名称" prop="factoryName">
          <el-select v-model="form.factoryName" placeholder="请选择加工厂名称" clearable style="width: 100%">
            <el-option v-for="item in factoryList" :key="item.id" :label="item.factoryName" :value="item.factoryName" />
          </el-select>
        </el-form-item>
        <el-form-item label="成品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入成品名称" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createDate">
          <el-date-picker
            style="width: 100%"
            clearable
            v-model="form.createDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="库存量" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入入库量" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Stock">
import { listStock, getStock, delStock, addStock, updateStock } from '@/api/system/stock';
import { listFactory } from '@/api/system/factory.js';

const { proxy } = getCurrentInstance();

const stockList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    factoryName: null,
    productName: null,
    createDate: null,
    amount: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);
const factoryList = ref([]);
/** 查询加工成品库存管理列表 */
function getList() {
  loading.value = true;
  listStock(queryParams.value).then((response) => {
    stockList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });

  listFactory().then((response) => {
    factoryList.value = response.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    factoryName: null,
    productName: null,
    createDate: null,
    amount: null
  };
  proxy.resetForm('stockRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加加工成品库存管理';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getStock(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改加工成品库存管理';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['stockRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateStock(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addStock(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除加工成品库存管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delStock(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/stock/export',
    {
      ...queryParams.value
    },
    `stock_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
