<template>
  <div class="app-container">
    <el-container>
      <el-aside width="300px" style="background-color: grey" >
        <el-input
            v-model="filterText"
            class="w-60 mb-2"
            placeholder="请输入基地或设备名称"
        />
        <el-tree
            ref="treeRef"
            :data="bases"
            :props="defaultProps"
            @node-click="handleNodeClick"
            node-key="key"
            :filter-node-method="filterNode"
        >
          <!-- 自定义节点渲染 -->
          <template #default="{ node, data }">
            <span class="custom-node">
              <span class="node-label" :title="node.label">{{ node.label }}</span>
              <!-- 如果是设备，显示状态 -->
              <span v-if="data.leaf" class="node-status">
                <span class="dot" :class="{
                  'online': data.status === '1',
                  'offline': data.status === '0'
                }"></span>
              </span>
            </span>
          </template>
        </el-tree>
      </el-aside>
      <el-main>
        <el-card v-if="selectedDevice" class="box-card">
          <template #header>
            <div class="card-header">
              <span>{{ selectedDevice.name }}</span>
              <div class="device-status">
                <span class="status-text">状态: </span>
                <span class="status-value" :class="{
                  'online-text': selectedDevice.status === '1',
                  'offline-text': selectedDevice.status === '0'
                }">
                  {{ selectedDevice.status === '1' ? '在线' : '离线' }}
                </span>
                <span class="last-update">最后更新: {{ formatTime(selectedDevice.updateTime) }}</span>
              </div>
            </div>
          </template>

          <!-- 监控画面区域 - 使用div代替图片 -->
          <div class="monitor-container">
            <div class="video-container">
              <div v-if="selectedDevice.status === '1'" class="video-placeholder">
                <div class="placeholder-content">
                  <el-icon :size="40"><VideoCamera /></el-icon>
                  <p>监控画面</p>
                </div>
              </div>
              <div v-else class="no-signal">
                <el-icon :size="50"><VideoCameraFilled /></el-icon>
                <p>无信号</p>
              </div>

              <div class="video-controls">
                <el-button-group>
                  <el-button type="primary" :icon="ZoomIn" @click="zoomIn" title="放大"></el-button>
                  <el-button type="primary" :icon="ZoomOut" @click="zoomOut" title="缩小"></el-button>
                  <el-button type="primary" :icon="FullScreen" @click="fullScreen" title="全屏"></el-button>
                  <el-button type="primary" :icon="Refresh" @click="refreshVideo" title="刷新"></el-button>
                  <el-button type="primary" :icon="VideoPlay" @click="startRecord" title="开始录像" v-if="!isRecording"></el-button>
                  <el-button type="danger" :icon="VideoPause" @click="stopRecord" title="停止录像" v-else></el-button>
                  <el-button type="primary" :icon="Camera" @click="capture" title="截图"></el-button>
                </el-button-group>
              </div>
            </div>

            <!-- 设备信息区域 -->
            <div class="device-info">
              <el-descriptions title="设备信息" :column="2" border>
                <el-descriptions-item label="设备ID">{{ selectedDevice.id }}</el-descriptions-item>
                <el-descriptions-item label="设备类型">{{ selectedDevice.type || 'N/A' }}</el-descriptions-item>
                <el-descriptions-item label="IP地址">{{ selectedDevice.ip || 'N/A' }}</el-descriptions-item>
                <el-descriptions-item label="位置">{{ selectedDevice.location || 'N/A' }}</el-descriptions-item>
                <el-descriptions-item label="分辨率">1920x1080</el-descriptions-item>
                <el-descriptions-item label="帧率">25 FPS</el-descriptions-item>
                <el-descriptions-item label="存储状态">
                  <el-progress :percentage="75" :format="formatStorage"></el-progress>
                </el-descriptions-item>
                <el-descriptions-item label="网络状态">
                  <el-progress :percentage="selectedDevice.status === '1' ? 90 : 0"
                               :status="selectedDevice.status === '1' ? 'success' : 'exception'"></el-progress>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 事件记录区域 -->
<!--            <div class="event-log">-->
<!--              <el-card shadow="hover">-->
<!--                <template #header>-->
<!--                  <div class="clearfix">-->
<!--                    <span>事件记录</span>-->
<!--                    <el-button style="float: right; padding: 3px 0" type="text" @click="clearEvents">清空</el-button>-->
<!--                  </div>-->
<!--                </template>-->
<!--                <el-timeline>-->
<!--                  <el-timeline-item-->
<!--                      v-for="(event, index) in events"-->
<!--                      :key="index"-->
<!--                      :timestamp="event.time">-->
<!--                    {{ event.content }}-->
<!--                  </el-timeline-item>-->
<!--                </el-timeline>-->
<!--              </el-card>-->
<!--            </div>-->
          </div>
        </el-card>
        <div v-else class="no-device">
          <el-empty description="请从左侧选择一个监控设备"></el-empty>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup >
import { ref, onMounted,watch } from 'vue'
import {
  ZoomIn, ZoomOut, FullScreen, Refresh,
  VideoPlay, VideoPause, Camera, VideoCameraFilled, VideoCamera
} from '@element-plus/icons-vue'
import { listBaseForMonitor } from "@/api/system/monitor.js"
import { ElMessage } from 'element-plus'
const bases = ref([])
const selectedDevice = ref(null)
const isRecording = ref(false)
const events = ref([
  { time: '2023-05-15 09:30:25', content: '设备启动完成' },
  { time: '2023-05-15 10:15:42', content: '检测到移动物体' },
  { time: '2023-05-15 11:02:18', content: '存储空间达到70%' },
  { time: '2023-05-15 12:45:33', content: '网络连接短暂中断' }
])

const defaultProps = {
  children: 'children',
  label: 'name',
  isLeaf: 'leaf'
}

const filterText = ref('')
const treeRef = ref();

watch(filterText, (val) => {
  console.log("val",val)
  if(treeRef.value){
    treeRef.value.filter(val)
  }
})

const filterNode = (value, data) => {
  if (!value) return true
  console.log("data",data)
  return data.name.includes(value)
}

const handleNodeClick = (data) => {
  if (!data.leaf) return
  selectedDevice.value = data
  // 模拟添加事件
  addEvent(`切换到设备 ${data.name}`)
}

const loadBases = () => {
  listBaseForMonitor().then(res => {
    if(res.code == 200) {
      bases.value = res.data
    }
  })
}

const formatTime = (time) => {
  if (!time) return 'N/A'
  return new Date(time).toLocaleString()
}

const formatStorage = () => {
  return '75% (15GB/20GB)'
}

const zoomIn = () => {
  ElMessage.success('放大画面')
}

const zoomOut = () => {
  ElMessage.success('缩小画面')
}

const fullScreen = () => {
  ElMessage.success('全屏显示')
}

const refreshVideo = () => {
  ElMessage.success('刷新画面')
  addEvent('手动刷新视频流')
}

const startRecord = () => {
  isRecording.value = true
  ElMessage.success('开始录像')
  addEvent('开始录像')
}

const stopRecord = () => {
  isRecording.value = false
  ElMessage.success('停止录像')
  addEvent('停止录像')
}

const capture = () => {
  ElMessage.success('截图已保存')
  addEvent('手动截图')
}

const addEvent = (content) => {
  const now = new Date()
  events.value.unshift({
    time: now.toLocaleString(),
    content: content
  })
  if (events.value.length > 50) {
    events.value.pop()
  }
}

const clearEvents = () => {
  events.value = []
  ElMessage.success('已清空事件记录')
}

onMounted(() => {
  loadBases()
})
</script>

<style scoped>

.box-card {
  width: 100%;
  height: 100%;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}
.device-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}
.status-text {
  margin-right: 5px;
}
.status-value {
  font-weight: bold;
  margin-right: 20px;
}
.online-text {
  color: #67c23a;
}
.offline-text {
  color: #909399;
}
.last-update {
  color: #909399;
  font-size: 12px;
}

/* 监控画面区域 */
.monitor-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.video-container {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}
.video-placeholder {
  width: 100%;
  height: 450px;
  background-color: #000;
  border: 2px solid red;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}
.placeholder-content {
  text-align: center;
}
.placeholder-content p {
  margin-top: 10px;
  font-size: 16px;
}
.no-signal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 450px;
  color: #fff;
  font-size: 18px;
  background-color: #000;
  border: 2px solid red;
}
.video-controls {
  display: flex;
  justify-content: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
}
.video-controls .el-button-group {
  margin: 0 auto;
}

/* 设备信息区域 */
.device-info {
  margin-top: 10px;
}

/* 事件记录区域 */
.event-log {
  margin-top: 20px;
}
.event-log .el-timeline {
  max-height: 200px;
  overflow-y: auto;
  padding: 0 10px;
}

/* 无设备选中状态 */
.no-device {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* 自定义节点内容 */
.custom-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  overflow: hidden;
}
.node-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.node-status {
  flex-shrink: 0;
  margin-left: 10px;
}
.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}
.online { background-color: #67c23a; } /* green */
.offline { background-color: #909399; } /* gray */
</style>
