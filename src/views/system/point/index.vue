<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="点位名称" prop="factoryName">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入点位名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:point:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:point:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:point:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="factoryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="点位名称" align="center" prop="name" />
      <el-table-column label="点位编码" lign="center" prop="code" />
      <el-table-column label="点位类型" align="center" prop="type">
        <template #default="scope">
          {{ getPointTypeLabel(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column label="点位值" align="center" prop="jsonValue" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:factory:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:factory:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改点位管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="pointRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="点位名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入点位名称" />
        </el-form-item>
        <el-form-item label="点位编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入点位编码" />
        </el-form-item>
        <el-form-item label="点位类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择点位类型">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="点位值" prop="jsonValue">
          <el-input
            v-model="form.jsonValue"
            type="textarea"
            :rows="4"
            placeholder="请输入点位值"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Factory">
import { listPoint, addPoint, getPoint, updatePoint, delPoint } from "@/api/system/point"

const { proxy } = getCurrentInstance()

const typeOptions = [
  { value: 'video', label: '视频' },
  { value: 'monitor', label: '视频流' },
  { value: 'info', label: '文本json' }
]

const getPointTypeLabel = (type) => {
  const option = typeOptions.find(item => item.value === type)
  return option ? option.label : type
}

const factoryList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    factoryName: null,
    factoryAddress: null
  },
  rules: {
    name: [
      { required: true, message: "点位名称不能为空", trigger: "blur" }
    ],
    code: [
      { required: true, message: "点位编码不能为空", trigger: "blur" },
      {
        pattern: /^[A-Za-z0-9]+$/,  // 只允许字母和数字
        message: "编码只能包含字母或数字",
        trigger: "blur"
      }
    ],
    type: [
      { required: true, message: "请选择点位类型", trigger: "change" }
    ],
    jsonValue: [
      { required: true, message: "点位值不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询点位管理列表 */
function getList() {
  loading.value = true
  listPoint(queryParams.value).then(response => {
    console.log(response)
    factoryList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    type: typeOptions[0]?.value || null,
    jsonValue: null
  }
  proxy.resetForm("pointRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加点位管理"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getPoint(_id).then(response => {
    console.log(response)
    form.value = response.data
    open.value = true
    title.value = "修改点位管理"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["pointRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updatePoint(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addPoint(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除点位管理编号为"' + _ids + '"的数据项？').then(function() {
    return delPoint(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/factory/export', {
    ...queryParams.value
  }, `factory_${new Date().getTime()}.xlsx`)
}

getList()
</script>
