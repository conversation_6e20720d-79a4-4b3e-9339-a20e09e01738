<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
      <el-form-item label="银行名称" prop="bankName">
        <el-input v-model="queryParams.bankName" clearable placeholder="请输入银行名称" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="放款金额" prop="amount">
        <el-input v-model="queryParams.amount" clearable placeholder="请输入放款金额" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="放款时间" prop="createDate">
        <el-date-picker
          v-model="queryParams.createDate"
          clearable
          placeholder="请选择放款时间"
          type="date"
          value-format="YYYY-MM-DD"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:lending:add']" icon="Plus" plain type="primary" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:lending:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:lending:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:lending:export']" icon="Download" plain type="warning" @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="lendingList" @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="" prop="id" />
      <el-table-column align="center" label="银行名称" prop="bankName" />
      <el-table-column align="center" label="放款金额(万元)" prop="amount" width="180" />
      <el-table-column align="center" label="放款时间" prop="createDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:lending:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['system:lending:remove']"
            icon="Delete"
            link
            type="primary"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改贷款管理对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="500px">
      <el-form ref="lendingRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="银行名称" prop="bankName">
          <el-input v-model="form.bankName" placeholder="请输入银行名称" />
        </el-form-item>
        <el-form-item label="放款金额(万元)" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入放款金额" />
        </el-form-item>
        <el-form-item label="放款时间" prop="createDate">
          <el-date-picker
            v-model="form.createDate"
            clearable
            placeholder="请选择放款时间"
            type="date"
            value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="Lending" setup>
import { listLending, getLending, delLending, addLending, updateLending } from '@/api/system/lending';

const { proxy } = getCurrentInstance();

const lendingList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bankName: null,
    amount: null,
    createDate: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询贷款管理列表 */
function getList() {
  loading.value = true;
  listLending(queryParams.value).then((response) => {
    lendingList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    bankName: null,
    amount: null,
    createDate: null
  };
  proxy.resetForm('lendingRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加贷款管理';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getLending(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改贷款管理';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['lendingRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateLending(form.value).then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addLending(form.value).then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除贷款管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delLending(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/lending/export',
    {
      ...queryParams.value
    },
    `lending_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
