<template>
  <div class="mapPickerContainer">
    <div class="mapPickerSearchBar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索地点..."
        size="small"
        class="mapPickerSearchInput"
        @keyup.enter="handleSearch"
      />
      <el-button size="small" @click="handleSearch">搜索</el-button>
    </div>
    <div :id="mapId" class="mapPickerMap"></div>
    <div v-if="point" class="mapPickerPointInfo">经度: {{ point.lng }}<br />纬度: {{ point.lat }}</div>
    <div class="mapPickerBtnBar">
      <el-button type="primary" size="small" @click="confirm">保存点位</el-button>
      <el-button size="small" @click="$emit('cancel')">取消</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
let localSearch = null;
const searchQuery = ref('');
const props = defineProps({
  initLng: [String, Number],
  initLat: [String, Number]
});
const emit = defineEmits(['confirm', 'cancel']);
const mapId = 'baidu-map-picker-' + Math.random().toString(36).slice(2);
const point = ref(null);
let map = null;
let marker = null;

function loadBMapScript() {
  return new Promise((resolve, reject) => {
    if (window.BMapGL) {
      resolve();
      return;
    }
    const script = document.createElement('script');
    script.src = '//api.map.baidu.com/api?type=webgl&v=1.0&ak=ConqWMS6IAb8BtGBbviXKMDNNeOUPgNH';
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

function initMap() {
  map = new window.BMapGL.Map(mapId);
  let center;
  if (props.initLng && props.initLat) {
    center = new window.BMapGL.Point(Number(props.initLng), Number(props.initLat));
    point.value = { lng: Number(props.initLng), lat: Number(props.initLat) };
  } else {
    center = new window.BMapGL.Point(127.1, 44.4);
  }
  map.centerAndZoom(center, 15);
  map.enableScrollWheelZoom();
  map.setMapType(window.BMAP_SATELLITE_MAP);
  if (point.value) {
    marker = new window.BMapGL.Marker(center);
    map.addOverlay(marker);
  }
  // 初始化本地搜索
  localSearch = new window.BMapGL.LocalSearch(map, {
    onSearchComplete: function (results) {
      if (localSearch.getStatus() === window.BMAP_STATUS_SUCCESS) {
        const poi = results.getPoi(0);
        if (poi) {
          const pt = poi.point;
          map.centerAndZoom(pt, 16);
          if (marker) map.removeOverlay(marker);
          marker = new window.BMapGL.Marker(pt);
          map.addOverlay(marker);
          point.value = { lng: pt.lng, lat: pt.lat };
        }
      }
    }
  });
  map.addEventListener('click', (e) => {
    point.value = { lng: e.latlng.lng, lat: e.latlng.lat };
    if (marker) map.removeOverlay(marker);
    marker = new window.BMapGL.Marker(e.latlng);
    map.addOverlay(marker);
  });
}

function confirm() {
  if (point.value) {
    emit('confirm', point.value);
  }
}

function handleSearch() {
  if (!searchQuery.value.trim() || !localSearch) return;
  localSearch.search(searchQuery.value.trim());
}

onMounted(async () => {
  await loadBMapScript();
  setTimeout(initMap, 100); // 等待DOM渲染
});

watch(
  () => [props.initLng, props.initLat],
  ([lng, lat]) => {
    if (map && lng && lat) {
      const p = new window.BMapGL.Point(Number(lng), Number(lat));
      map.centerAndZoom(p, 15);
      if (marker) map.removeOverlay(marker);
      marker = new window.BMapGL.Marker(p);
      map.addOverlay(marker);
      point.value = { lng: Number(lng), lat: Number(lat) };
    }
  }
);
</script>
<style scoped>
.mapPickerContainer {
  width: 100%;
  height: 420px;
  position: relative;
}
.mapPickerSearchBar {
  position: absolute;
  top: 10px;
  left: 250px;
  z-index: 20;
  display: flex;
  align-items: center;
}
.mapPickerSearchInput {
  width: 220px;
  margin-right: 8px;
}
.mapPickerMap {
  width: 100%;
  height: 100%;
}
.mapPickerPointInfo {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 10;
}
.mapPickerBtnBar {
  position: absolute;
  bottom: 10px;
  right: 20px;
  z-index: 10;
}
</style>
