<template>
  <div class="map-drawer-modal">
    <div class="map-drawer-container">
      <!-- 搜索框 -->
      <div class="search-box">
        <input type="text" v-model="searchQuery" placeholder="搜索地点..." @keyup.enter="handleSearch" />
        <button @click="handleSearch">搜索</button>
      </div>
      <div style="position: absolute; top: 60px; left: 30px; z-index: 10">
        <button
          @click="toggleMapType"
          style="padding: 6px 16px; border-radius: 4px; background: #1890ff; color: #fff; border: none; cursor: pointer"
        >
          切换地图类型
        </button>
      </div>
      <div id="map-container"></div>
      <div class="map-controls">
        <button @click="startDrawing" :disabled="isDrawing">开始绘制</button>
        <button @click="clearDrawing">清除绘制</button>
        <button @click="saveAndClose">保存并关闭</button>
        <div>
          <button @click="toggleMapType">切换地图类型</button>
        </div>
        <button @click="closeModal">取消</button>
        <div class="instructions">
          <p>右键点击地图添加顶点，左键点击第一个红色标记完成绘制</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapDrawer',
  props: {
    initialPoints: {
      type: Array,
      default: () => []
    },
    lnt: {
      type: Number
    },
    lat: {
      type: Number
    }
  },
  data() {
    return {
      searchQuery: '',
      localSearch: null,
      searchMarker: null,
      map: null,
      isDrawing: false,
      polygon: null,
      points: [],
      markers: [],
      initialCenter: [127.1, 44.4],
      polyline: null,
      firstMarker: null,
      mapType: 'satellite' // 默认卫星地图
    };
  },
  mounted() {
    //如果有初始点，则加载初始点
    if (this.lnt && this.lat) {
      this.initialCenter = [this.lnt, this.lat];
    } else {
      this.initialCenter = [127.1, 44.4];
    }
    this.initMap();
    if (this.initialPoints && this.initialPoints.length > 0) {
      this.loadInitialPoints();
    }
  },
  methods: {
    initMap() {
      this.map = new window.BMapGL.Map('map-container');
      this.map.centerAndZoom(new window.BMapGL.Point(this.initialCenter[0], this.initialCenter[1]), 13);
      this.map.enableScrollWheelZoom();
      this.map.addControl(new window.BMapGL.NavigationControl());
      this.map.addControl(new window.BMapGL.ScaleControl());
      this.map.setMinZoom(1);
      this.map.setMaxZoom(17);
      // 默认卫星地图
      this.map.setMapType(window.BMAP_SATELLITE_MAP);

      // 初始化本地搜索
      this.localSearch = new window.BMapGL.LocalSearch(this.map, {
        renderOptions: { map: this.map, panel: null }
      });
    },

    toggleMapType() {
      if (!this.map) return;
      if (this.mapType === 'satellite') {
        this.map.setMapType(window.BMAP_NORMAL_MAP);
        this.mapType = 'normal';
      } else {
        this.map.setMapType(window.BMAP_SATELLITE_MAP);
        this.mapType = 'satellite';
      }
    },

    loadInitialPoints() {
      // 将初始点转换为百度地图的点
      this.points = this.initialPoints.map((p) => new window.BMapGL.Point(p[0], p[1]));

      // 创建多边形
      this.polygon = new window.BMapGL.Polygon(this.points, {
        strokeColor: '#3388ff',
        strokeWeight: 4,
        strokeOpacity: 0.8,
        fillColor: '#3388ff',
        fillOpacity: 0.2
      });
      this.map.addOverlay(this.polygon);

      // 添加标记点
      this.points.forEach((point, index) => {
        const marker = new window.BMapGL.Marker(point);

        // 第一个点设置为红色
        if (index === 0) {
          const icon = new window.BMapGL.Icon(
            'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="red"/></svg>',
            new window.BMapGL.Size(24, 24),
            {
              anchor: new window.BMapGL.Size(12, 12),
              imageSize: new window.BMapGL.Size(24, 24)
            }
          );
          marker.setIcon(icon);
          marker.setZIndex(100);
          this.firstMarker = marker;

          // 为第一个点添加点击事件
          marker.addEventListener('click', () => {
            if (this.points.length >= 3) {
              this.completeDrawing();
            } else {
              alert('至少需要3个点才能构成多边形');
            }
          });
        } else {
          // 其他点设置为蓝色
          const icon = new window.BMapGL.Icon(
            'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="blue"/></svg>',
            new window.BMapGL.Size(20, 20),
            {
              anchor: new window.BMapGL.Size(10, 10),
              imageSize: new window.BMapGL.Size(20, 20)
            }
          );
          marker.setIcon(icon);
        }

        this.map.addOverlay(marker);
        this.markers.push(marker);
      });

      // 启用编辑功能
      this.polygon.enableEditing();
      this.polygon.addEventListener('lineupdate', this.handlePolygonUpdate);

      // 调整视图
      this.map.setViewport(this.points);
    },

    // 处理搜索
    handleSearch() {
      if (!this.searchQuery.trim()) return;

      this.localSearch.search(this.searchQuery);
    },

    startDrawing() {
      if (this.isDrawing) {
        alert('已经在绘制模式中');
        return;
      }

      this.clearDrawing();
      this.isDrawing = true;

      // 添加右键事件监听
      this.map.addEventListener('rightclick', this.handleRightClick);
    },

    handleRightClick(e) {
      if (!this.isDrawing) return;

      const point = e.latlng;
      this.points.push(point);

      // 创建标记点
      const marker = new window.BMapGL.Marker(point);

      // 如果是第一个点，设置为红色并添加点击事件
      if (this.points.length === 1) {
        const icon = new window.BMapGL.Icon(
          'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="red"/></svg>',
          new window.BMapGL.Size(24, 24),
          {
            anchor: new window.BMapGL.Size(12, 12),
            imageSize: new window.BMapGL.Size(24, 24)
          }
        );
        marker.setIcon(icon);
        marker.setZIndex(100);
        this.firstMarker = marker;

        marker.addEventListener('click', () => {
          if (this.points.length >= 3) {
            this.completeDrawing();
          } else {
            alert('至少需要3个点才能构成多边形');
          }
        });
      } else {
        // 其他点设置为蓝色
        const icon = new window.BMapGL.Icon(
          'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="blue"/></svg>',
          new window.BMapGL.Size(20, 20),
          {
            anchor: new window.BMapGL.Size(10, 10),
            imageSize: new window.BMapGL.Size(20, 20)
          }
        );
        marker.setIcon(icon);
      }

      this.map.addOverlay(marker);
      this.markers.push(marker);

      // 更新连线
      this.updatePolyline();
    },

    updatePolyline() {
      if (this.polyline) {
        this.map.removeOverlay(this.polyline);
      }

      if (this.points.length > 1) {
        this.polyline = new window.BMapGL.Polyline(this.points, {
          strokeColor: '#3388ff',
          strokeWeight: 2,
          strokeOpacity: 0.8,
          strokeStyle: 'dashed'
        });
        this.map.addOverlay(this.polyline);
      }
    },

    completeDrawing() {
      if (this.points.length < 3) {
        alert('至少需要3个点才能构成多边形');
        return;
      }

      // 创建多边形
      this.polygon = new window.BMapGL.Polygon(this.points, {
        strokeColor: '#3388ff',
        strokeWeight: 4,
        strokeOpacity: 0.8,
        fillColor: '#3388ff',
        fillOpacity: 0.2
      });
      this.map.addOverlay(this.polygon);

      // 移除临时连线
      if (this.polyline) {
        this.map.removeOverlay(this.polyline);
        this.polyline = null;
      }

      // 退出绘制模式
      this.isDrawing = false;
      this.map.removeEventListener('rightclick', this.handleRightClick);

      // 添加编辑功能
      this.polygon.enableEditing();
      this.polygon.addEventListener('lineupdate', this.handlePolygonUpdate);
    },

    handlePolygonUpdate() {
      // 多边形更新时的处理
      const updatedPoints = this.polygon.getPath();
      this.points = updatedPoints;

      // 更新标记点
      this.markers.forEach((marker) => this.map.removeOverlay(marker));
      this.markers = [];

      updatedPoints.forEach((point, index) => {
        const marker = new window.BMapGL.Marker(point);

        if (index === 0) {
          const icon = new window.BMapGL.Icon(
            'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="red"/></svg>',
            new window.BMapGL.Size(24, 24),
            {
              anchor: new window.BMapGL.Size(12, 12),
              imageSize: new window.BMapGL.Size(24, 24)
            }
          );
          marker.setIcon(icon);
          marker.setZIndex(100);
          this.firstMarker = marker;

          marker.addEventListener('click', () => {
            if (this.points.length >= 3) {
              this.completeDrawing();
            } else {
              alert('至少需要3个点才能构成多边形');
            }
          });
        } else {
          const icon = new window.BMapGL.Icon(
            'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="blue"/></svg>',
            new window.BMapGL.Size(20, 20),
            {
              anchor: new window.BMapGL.Size(10, 10),
              imageSize: new window.BMapGL.Size(20, 20)
            }
          );
          marker.setIcon(icon);
        }

        this.map.addOverlay(marker);
        this.markers.push(marker);
      });
    },

    clearDrawing() {
      if (this.polygon) {
        this.map.removeOverlay(this.polygon);
        this.polygon = null;
      }
      if (this.polyline) {
        this.map.removeOverlay(this.polyline);
        this.polyline = null;
      }
      this.markers.forEach((marker) => this.map.removeOverlay(marker));
      this.markers = [];
      this.points = [];
      this.firstMarker = null;
      this.isDrawing = false;

      this.map.removeEventListener('rightclick', this.handleRightClick);
    },

    saveAndClose() {
      if (this.points.length < 3) {
        alert('请先绘制有效的多边形区域');
        return;
      }

      // 获取坐标点数组
      const coordinates = this.points.map((point) => [point.lng, point.lat]);
      // 闭合多边形
      coordinates.push([coordinates[0][0], coordinates[0][1]]);

      this.$emit('save', coordinates);
      this.closeModal();
    },

    closeModal() {
      this.$emit('close');
    }
  },
  beforeDestroy() {
    // 清理地图资源
    if (this.map) {
      this.map.removeEventListener('rightclick', this.handleRightClick);
      this.clearDrawing();
    }
  }
};
</script>

<style scoped>
.map-drawer-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.map-drawer-container {
  width: 80%;
  height: 80%;
  background-color: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
}

.search-box {
  padding: 10px;
  display: flex;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.search-box input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 8px;
}

.search-box button {
  padding: 8px 15px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

#map-container {
  flex: 1;
  width: 100%;
}

.map-controls {
  padding: 15px;
  background: #f5f5f5;
  border-top: 1px solid #ddd;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.map-controls button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.map-controls button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.map-controls button:first-child {
  background-color: #1890ff;
  color: white;
}

.map-controls button:nth-child(2) {
  background-color: #ff4d4f;
  color: white;
}

.map-controls button:nth-child(3) {
  background-color: #52c41a;
  color: white;
}

.map-controls button:nth-child(4) {
  background-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.65);
}

.instructions {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  width: 100%;
}
</style>
