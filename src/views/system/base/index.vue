<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="100px">
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          clearable
          placeholder="请选择创建时间"
          type="date"
          value-format="YYYY-MM-DD"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="修改时间" prop="updateTime">
        <el-date-picker
          v-model="queryParams.updateTime"
          clearable
          placeholder="请选择修改时间"
          type="date"
          value-format="YYYY-MM-DD"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="创建人" prop="createUserId">
        <el-input v-model="queryParams.createUserId" clearable placeholder="请输入创建人" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="修改人" prop="updateUserId">
        <el-input v-model="queryParams.updateUserId" clearable placeholder="请输入修改人" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="基地名称" prop="baseName">
        <el-input v-model="queryParams.baseName" clearable placeholder="请输入基地名称" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="负责人" prop="leader">
        <el-input v-model="queryParams.leader" clearable placeholder="请输入负责人" @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:base:add']" icon="Plus" plain type="primary" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:base:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:base:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:base:export']" icon="Download" plain type="warning" @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="baseList" @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="ID" prop="id" />
      <el-table-column align="center" label="状态" prop="auditStatus" />
      <el-table-column align="center" label="创建时间" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="修改时间" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建人" prop="createUserId" />
      <el-table-column align="center" label="修改人" prop="updateUserId" />
      <el-table-column align="center" label="基地名称" prop="baseName" />
      <el-table-column align="center" label="负责人" prop="leader" />
      <el-table-column align="center" label="电话" prop="tel" />
      <el-table-column align="center" label="面积" prop="area" />
      <el-table-column align="center" label="成立日期" prop="establishDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.establishDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="围栏长度(米)" prop="fencePerimeter" width="120">
        <template #default="scope">
          <div>
            {{ scope.row.fencePerimeter }}
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="经度" prop="longitude" />
      <el-table-column align="center" label="纬度" prop="latitude" />
      <el-table-column align="center" label="省" prop="provinceCode" />
      <el-table-column align="center" label="市" prop="cityCode" />
      <el-table-column align="center" label="县" prop="countyCode" />
      <el-table-column align="center" label="乡" prop="townsCode" />
      <el-table-column align="center" label="村" prop="villageCode" />
      <!-- <el-table-column label="信用等级" align="center" prop="creditLevel" /> -->
      <el-table-column align="center" label="宜参林地" prop="ysld" />
      <el-table-column align="center" label="宜参耕地" prop="ysgd" />
      <el-table-column align="center" label="林地已种面积" prop="ldyzmj" />
      <el-table-column align="center" label="耕地已种面积" prop="gdyzmj" />
      <el-table-column align="center" label="基地类型" prop="baseType">
        <template #default="scope">
          <dict-tag :options="base_type" :value="scope.row.baseType" />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="150">
        <template #default="scope">
          <div style="display: flex">
            <el-button
              v-hasPermi="['system:base:edit']"
              icon="Edit"
              link
              type="primary"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              v-hasPermi="['system:base:remove']"
              icon="Delete"
              link
              type="primary"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改基地对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="600px">
      <el-form ref="baseRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="创建人" prop="createUserId">
          <el-input v-model="form.createUserId" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="修改人" prop="updateUserId">
          <el-input v-model="form.updateUserId" placeholder="请输入修改人" />
        </el-form-item>
        <el-form-item label="基地名称" prop="baseName">
          <el-input v-model="form.baseName" placeholder="请输入基地名称" />
        </el-form-item>
        <el-form-item label="负责人" prop="leader">
          <el-input v-model="form.leader" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="电话" prop="tel">
          <el-input v-model="form.tel" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="面积" prop="area">
          <el-input v-model="form.area" placeholder="请输入面积" />
        </el-form-item>
        <el-form-item label="成立日期" prop="establishDate">
          <el-date-picker
            v-model="form.establishDate"
            clearable
            placeholder="请选择成立日期"
            type="date"
            value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" style="width: 70%; margin-right: 8px" />
          <el-button type="primary" @click="showLngLatMap = true">获取经纬度</el-button>
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="省" prop="provinceCode">
          <el-input v-model="form.provinceCode" placeholder="请输入省" />
        </el-form-item>
        <el-form-item label="市" prop="cityCode">
          <el-input v-model="form.cityCode" placeholder="请输入市" />
        </el-form-item>
        <el-form-item label="县" prop="countyCode">
          <el-input v-model="form.countyCode" placeholder="请输入县" />
        </el-form-item>
        <el-form-item label="乡" prop="townsCode">
          <el-input v-model="form.townsCode" placeholder="请输入乡" />
        </el-form-item>
        <el-form-item label="村" prop="villageCode">
          <el-input v-model="form.villageCode" placeholder="请输入村" />
        </el-form-item>
        <!-- <el-form-item label="信用等级" prop="creditLevel">
          <el-input v-model="form.creditLevel" placeholder="请输入信用等级" />
        </el-form-item> -->
        <el-form-item label="宜参林地" prop="ysld">
          <el-input v-model="form.ysld" placeholder="请输入宜参林地" />
        </el-form-item>
        <el-form-item label="林地已种面积" prop="ldyzmj">
          <el-input v-model="form.ldyzmj" placeholder="请输入林地已种面积" />
        </el-form-item>
        <el-form-item label="宜参耕地" prop="ysgd">
          <el-input v-model="form.ysgd" placeholder="请输入宜参耕地" />
        </el-form-item>

        <el-form-item label="耕地已种面积" prop="gdyzmj">
          <el-input v-model="form.gdyzmj" placeholder="请输入耕地已种面积" />
        </el-form-item>
        <el-form-item label="基地类型" prop="baseType">
          <el-select v-model="form.baseType" placeholder="请选择基地类型">
            <el-option v-for="dict in base_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经纬度json串" prop="baseLocation">
          <el-button v-if="coordinates.length <= 0" type="primary" @click="openMapDrawer('baseLocation')"
            >绘制区域</el-button
          >
          <div v-if="coordinates.length > 0" class="coordinates-preview">
            <p>已绘制区域，包含 {{ coordinates.length }} 个顶点</p>
            <el-button type="primary" @click="openMapDrawer('baseLocation')">重新绘制</el-button>
          </div>
        </el-form-item>
        <el-form-item label="基地围栏" prop="fenceLocation">
          <el-button v-if="fenceCoordinates.length <= 0" type="primary" @click="openMapDrawer('fenceLocation')"
            >绘制围栏</el-button
          >
          <div v-if="fenceCoordinates.length > 0" class="coordinates-preview">
            <p>已绘制围栏，包含 {{ fenceCoordinates.length }} 个顶点</p>
            <el-button type="primary" @click="openMapDrawer('fenceLocation')">重新绘制</el-button>
          </div>
        </el-form-item>
      </el-form>
      <!-- 地图绘制弹窗 -->
      <MapDrawer
        v-if="showMapDrawer"
        :initial-points="mapDrawerType === 'baseLocation' ? coordinates : fenceCoordinates"
        :lat="form.latitude"
        :lnt="form.longitude"
        @close="closeMapDrawer"
        @save="handleSaveCoordinates"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="showLngLatMap" append-to-body title="选择经纬度" width="700px">
      <BaiduMapPicker
        v-if="showLngLatMap"
        :init-lat="form.latitude"
        :init-lng="form.longitude"
        @cancel="showLngLatMap = false"
        @confirm="handleLngLatConfirm"
      />
    </el-dialog>
  </div>
</template>

<script name="Base" setup>
import { listBase, getBase, delBase, addBase, updateBase } from '@/api/system/base';
import MapDrawer from './components/map.vue';
import { ref } from 'vue';
import BaiduMapPicker from './components/BaiduMapPicker.vue';
import { reactive, toRefs } from 'vue';
const { proxy } = getCurrentInstance();
const { base_type } = proxy.useDict('base_type');
const showMapDrawer = ref(false);
const coordinates = ref([]);
const showLngLatMap = ref(false);
const fenceCoordinates = ref([]);
const mapDrawerType = ref('baseLocation');

function handleLngLatConfirm({ lng, lat }) {
  form.value.longitude = lng;
  form.value.latitude = lat;
  showLngLatMap.value = false;
}

const openMapDrawer = (type = 'baseLocation') => {
  mapDrawerType.value = type;
  showMapDrawer.value = true;
};

const closeMapDrawer = () => {
  showMapDrawer.value = false;
};

const handleSaveCoordinates = (coords) => {
  if (mapDrawerType.value === 'baseLocation') {
    coordinates.value = coords;
  } else {
    fenceCoordinates.value = coords;
  }
  closeMapDrawer();
};
const baseList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    auditStatus: null,
    createTime: null,
    updateTime: null,
    createUserId: null,
    updateUserId: null,
    baseName: null,
    leader: null,
    tel: null,
    area: null,
    establishDate: null,
    longitude: null,
    latitude: null,
    provinceCode: null,
    cityCode: null,
    countyCode: null,
    townsCode: null,
    villageCode: null,
    // creditLevel: null,
    ysld: null,
    ysgd: null,
    ldyzmj: null,
    gdyzmj: null,
    baseType: null,
    baseLocation: null,
    fenceLocation: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询基地列表 */
function getList() {
  loading.value = true;
  listBase(queryParams.value).then((response) => {
    baseList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    auditStatus: null,
    createTime: null,
    updateTime: null,
    createUserId: null,
    updateUserId: null,
    baseName: null,
    leader: null,
    tel: null,
    area: null,
    establishDate: null,
    longitude: null,
    latitude: null,
    provinceCode: null,
    cityCode: null,
    countyCode: null,
    townsCode: null,
    villageCode: null,
    // creditLevel: null,
    ysld: null,
    ysgd: null,
    ldyzmj: null,
    gdyzmj: null,
    baseType: null,
    baseLocation: null,
    fenceLocation: null
  };
  coordinates.value = [];
  fenceCoordinates.value = [];
  proxy.resetForm('baseRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  showMapDrawer.value = false;
  reset();
  open.value = true;
  title.value = '添加基地';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  showMapDrawer.value = false;
  reset();
  const _id = row.id || ids.value;
  getBase(_id).then((response) => {
    form.value = response.data;
    if (response.data.baseLocation) {
      coordinates.value = JSON.parse(response.data.baseLocation);
    } else {
      coordinates.value = [];
    }
    if (response.data.fenceLocation) {
      fenceCoordinates.value = JSON.parse(response.data.fenceLocation);
    } else {
      fenceCoordinates.value = [];
    }
    open.value = true;
    title.value = '修改基地';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['baseRef'].validate((valid) => {
    if (valid) {
      form.value.baseLocation = JSON.stringify(coordinates.value);
      form.value.fenceLocation = JSON.stringify(fenceCoordinates.value);
      if (form.value.id != null) {
        updateBase(form.value).then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addBase(form.value).then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除基地编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delBase(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/base/export',
    {
      ...queryParams.value
    },
    `base_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
