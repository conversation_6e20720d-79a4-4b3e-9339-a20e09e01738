<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="基地ID" prop="baseId">
        <el-input v-model="queryParams.baseId" placeholder="请输入基地ID" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="基地名称" prop="baseName">
        <el-input v-model="queryParams.baseName" placeholder="请输入基地名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item label="采收时间" prop="harvestDate">
        <el-date-picker
          clearable
          v-model="queryParams.harvestDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择采收时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="种植时间" prop="plantDate">
        <el-date-picker
          clearable
          v-model="queryParams.plantDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择种植时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="人参编码" prop="ginsengCode">
        <el-input v-model="queryParams.ginsengCode" placeholder="请输入人参编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item label="主体名称" prop="enterpriseName">
        <el-input
          v-model="queryParams.enterpriseName"
          placeholder="请输入主体名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:ginseng:add']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:ginseng:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:ginseng:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ginsengList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="基地ID" align="center" prop="baseId" />
      <el-table-column label="基地名称" align="center" prop="baseName" />
      <el-table-column label="采收数量" align="center" prop="harvestCount" />
      <el-table-column label="采收时间" align="center" prop="harvestDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.harvestDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="种植时间" align="center" prop="plantDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.plantDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="人参类型" align="center" prop="ginsengTypeName"> </el-table-column>
      <el-table-column label="人参编码" align="center" prop="ginsengCode" />
      <el-table-column label="人参状态" align="center" prop="ginsengStatus" />
      <el-table-column label="种植面积(公顷)" align="center" prop="area" />
      <el-table-column label="主体名称" align="center" prop="enterpriseName" />
      <el-table-column label="人参保有量" align="center" prop="byl" />
      <el-table-column label="人参产量" align="center" prop="cl" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:ginseng:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:ginseng:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改种植管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="ginsengRef" :model="sendform" :rules="rules" label-width="110px">
        <el-form-item label="基地ID" prop="baseId">
          <el-input v-model="sendform.baseId" placeholder="请输入基地ID" />
        </el-form-item>
        <el-form-item label="基地名称" prop="baseName">
          <el-input v-model="sendform.baseName" placeholder="请输入基地名称" />
        </el-form-item>
        <el-form-item label="采收数量" prop="harvestCount">
          <el-input v-model="sendform.harvestCount" placeholder="请输入采收数量" />
        </el-form-item>
        <el-form-item label="采收时间" prop="harvestDate">
          <el-date-picker
            clearable
            v-model="sendform.harvestDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择采收时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="种植时间" prop="plantDate">
          <el-date-picker
            clearable
            v-model="sendform.plantDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择种植时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="人参编码" prop="ginsengCode">
          <el-input v-model="sendform.ginsengCode" placeholder="请输入人参编码" />
        </el-form-item>
        <el-form-item label="种植面积(公顷)" prop="area">
          <el-input v-model="sendform.area" placeholder="请输入种植面积" />
        </el-form-item>
        <el-form-item label="主体名称" prop="enterpriseName">
          <el-input v-model="sendform.enterpriseName" placeholder="请输入主体名称" />
        </el-form-item>
        <el-form-item label="人参保有量" prop="byl">
          <el-input v-model="sendform.byl" placeholder="请输入人参保有量" />
        </el-form-item>
        <el-form-item label="人参产量" prop="cl">
          <el-input v-model="sendform.cl" placeholder="请输入人参产量" />
        </el-form-item>
        <el-form-item label="人参类型" prop="ginsengType">
          <el-select v-model="sendform.ginsengType" placeholder="请选择人参类型">
            <el-option v-for="item in rs_lx" :key="item.code" :label="item.label" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ginseng">
import { listGinseng, getGinseng, delGinseng, addGinseng, updateGinseng } from '@/api/system/ginseng';

const { proxy } = getCurrentInstance();
const { rs_lx } = proxy.useDict('rs_lx');
const ginsengList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  sendform: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    baseId: null,
    baseName: null,
    harvestCount: null,
    harvestDate: null,
    plantDate: null,
    ginsengType: null,
    ginsengCode: null,
    ginsengStatus: null,
    area: null,
    enterpriseName: null,
    byl: null,
    cl: null
  },
  rules: {}
});

const { queryParams, sendform, rules } = toRefs(data);

/** 查询种植管理列表 */
function getList() {
  loading.value = true;
  listGinseng(queryParams.value).then((response) => {
    ginsengList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  sendform.value = {
    id: null,
    createTime: null,
    baseId: null,
    baseName: null,
    harvestCount: null,
    harvestDate: null,
    plantDate: null,
    ginsengType: null,
    ginsengCode: null,
    ginsengStatus: null,
    area: null,
    enterpriseName: null,
    byl: null,
    cl: null
  };
  proxy.resetForm('ginsengRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加种植管理';
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  const _id = row.id || ids.value;
  let response = await getGinseng(_id);
  sendform.value = response.data;
  sendform.value.ginsengType = response.data.ginsengType * 1;
  open.value = true;
  title.value = '修改种植管理';
};

/** 提交按钮 */
function submitForm() {
  proxy.$refs['ginsengRef'].validate((valid) => {
    if (valid) {
      if (sendform.value.id != null) {
        updateGinseng(sendform.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addGinseng(sendform.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除种植管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delGinseng(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/ginseng/export',
    {
      ...queryParams.value
    },
    `ginseng_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
