<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="原料" prop="origin">
        <el-input v-model="queryParams.origin" placeholder="请输入原料" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="产地" prop="address">
        <el-input v-model="queryParams.address" placeholder="请输入产地" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          v-model="queryParams.enterpriseName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:ginseng_product:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:ginseng_product:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:ginseng_product:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:ginseng_product:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ginseng_productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="原料" align="center" prop="origin" />
      <el-table-column label="产地" align="center" prop="address" />
      <el-table-column label="企业名称" align="center" prop="enterpriseName" />
      <el-table-column label="酒精浓度" align="center" prop="alcohol" />
      <el-table-column label="批次号" align="center" prop="batchNumber" />
      <el-table-column label="农药残留" align="center" prop="pesticide" />
      <el-table-column label="保质期" align="center" prop="validTime" />
      <el-table-column label="重金属" align="center" prop="metal" />
      <el-table-column label="参龄(年)" align="center" prop="level" />
      <el-table-column label="图片" align="center" prop="picture" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:ginseng_product:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:ginseng_product:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改人参产品信息管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="ginseng_productRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="原料" prop="origin">
          <el-input v-model="form.origin" placeholder="请输入原料" />
        </el-form-item>
        <el-form-item label="产地" prop="address">
          <el-input v-model="form.address" placeholder="请输入产地" />
        </el-form-item>
        <el-form-item label="企业名称" prop="enterpriseName">
          <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="酒精浓度" prop="alcohol">
          <el-input v-model="form.alcohol" placeholder="请输入酒精浓度" />
        </el-form-item>
        <el-form-item label="批次号" prop="batchNumber">
          <el-input v-model="form.batchNumber" placeholder="请输入批次号" />
        </el-form-item>
        <el-form-item label="农药残留" prop="pesticide">
          <el-input v-model="form.pesticide" placeholder="请输入农药残留" />
        </el-form-item>
        <el-form-item label="保质期" prop="validTime">
          <el-input v-model="form.validTime" placeholder="请输入保质期" />
        </el-form-item>
        <el-form-item label="重金属" prop="metal">
          <el-input v-model="form.metal" placeholder="请输入重金属" />
        </el-form-item>
        <el-form-item label="参龄" prop="level">
          <div style="display: flex">
            <el-input v-model="form.level" placeholder="请输入参龄" />
            <div style="margin-left: 10px">年</div>
          </div>
        </el-form-item>
        <el-form-item label="图片" prop="picture">
          <file-upload v-model="form.picture" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ginseng_product">
import {
  listGinseng_product,
  getGinseng_product,
  delGinseng_product,
  addGinseng_product,
  updateGinseng_product
} from '@/api/system/ginseng_product';

const { proxy } = getCurrentInstance();

const ginseng_productList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productName: null,
    origin: null,
    address: null,
    enterpriseName: null,
    alcohol: null,
    batchNumber: null,
    pesticide: null,
    validTime: null,
    metal: null,
    level: null,
    picture: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询人参产品信息管理列表 */
function getList() {
  loading.value = true;
  listGinseng_product(queryParams.value).then((response) => {
    ginseng_productList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    productName: null,
    origin: null,
    address: null,
    enterpriseName: null,
    alcohol: null,
    batchNumber: null,
    pesticide: null,
    validTime: null,
    metal: null,
    level: null,
    picture: null
  };
  proxy.resetForm('ginseng_productRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加人参产品信息管理';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getGinseng_product(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改人参产品信息管理';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['ginseng_productRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateGinseng_product(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addGinseng_product(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除人参产品信息管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delGinseng_product(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/ginseng_product/export',
    {
      ...queryParams.value
    },
    `ginseng_product_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
