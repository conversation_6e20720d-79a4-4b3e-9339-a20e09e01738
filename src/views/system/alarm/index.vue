<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="时间" prop="createTime">
        <el-date-picker
          clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="标题" prop="alarmTitle">
        <el-input v-model="queryParams.alarmTitle" placeholder="请输入标题" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="告警类型" prop="alarmType">
        <el-select v-model="queryParams.alarmType" placeholder="请选择告警类型" clearable style="width: 150px">
          <el-option v-for="dict in alarm_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="基地名称" prop="baseName">
        <el-input v-model="queryParams.baseName" placeholder="请输入基地名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:alarm:add']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:alarm:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:alarm:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="alarmList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="center" prop="alarmTitle" />
      <el-table-column label="告警类型" align="center" prop="alarmType">
        <template #default="scope">
          <dict-tag :options="alarm_type" :value="scope.row.alarmType" />
        </template>
      </el-table-column>
      <el-table-column label="说明" align="center" prop="alarmContent" />
      <el-table-column label="原因" align="center" prop="cause" />
      <el-table-column label="企业名称" align="center" prop="enterpriseName" />
      <el-table-column label="负责人" align="center" prop="leader" />
      <el-table-column label="联系方式" align="center" prop="tel" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="基地名称" align="center" prop="baseName" />
      <el-table-column label="告警时长" align="center" prop="alarmTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:alarm:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:alarm:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改告警对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="alarmRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="alarmTitle">
          <el-input v-model="form.alarmTitle" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="告警类型" prop="alarmType">
          <el-select v-model="form.alarmType" placeholder="请选择告警类型">
            <el-option v-for="dict in alarm_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明" prop="alarmContent">
          <el-input v-model="form.alarmContent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="原因" prop="cause">
          <el-input v-model="form.cause" placeholder="请输入原因" />
        </el-form-item>
        <el-form-item label="企业名称" prop="enterpriseName">
          <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="负责人" prop="leader">
          <el-input v-model="form.leader" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="联系方式" prop="tel">
          <el-input v-model="form.tel" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="基地名称" prop="baseName">
          <el-input v-model="form.baseName" placeholder="请输入基地名称" />
        </el-form-item>
        <el-form-item label="告警时长" prop="alarmTime">
          <el-input v-model="form.alarmTime" placeholder="请输入告警时长" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Alarm">
import { listAlarm, getAlarm, delAlarm, addAlarm, updateAlarm } from '@/api/system/alarm';

const { proxy } = getCurrentInstance();
const { alarm_type } = proxy.useDict('alarm_type');

const alarmList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    createTime: null,
    alarmTitle: null,
    alarmType: null,
    cause: null,
    enterpriseName: null,
    leader: null,
    tel: null,
    deviceName: null,
    baseName: null,
    alarmTime: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询告警列表 */
function getList() {
  loading.value = true;
  listAlarm(queryParams.value).then((response) => {
    alarmList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    createTime: null,
    alarmTitle: null,
    alarmType: null,
    alarmContent: null,
    cause: null,
    deviceId: null,
    baseId: null,
    enterpriseName: null,
    leader: null,
    tel: null,
    deviceName: null,
    baseName: null,
    alarmTime: null
  };
  proxy.resetForm('alarmRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加告警';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getAlarm(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改告警';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['alarmRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateAlarm(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addAlarm(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除告警编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delAlarm(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/alarm/export',
    {
      ...queryParams.value
    },
    `alarm_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
