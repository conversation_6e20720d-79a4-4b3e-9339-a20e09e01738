<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
      <el-form-item label="购买人" prop="customer">
        <el-input v-model="queryParams.customer" placeholder="请输入购买人" />
      </el-form-item>
      <el-form-item label="购买数量" prop="amount">
        <el-input v-model="queryParams.amount" placeholder="请输入购买数量" />
      </el-form-item>

      <el-form-item label="销售主体" prop="saler">
        <el-input v-model="queryParams.saler" placeholder="请输入销售主体" />
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" clearable placeholder="请选择订单状态" style="width: 198px">
          <el-option v-for="dict in task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!--suppress VueUnrecognizedDirective -->
        <el-button v-hasPermi="['system:orders:add']" icon="Plus" plain type="primary" @click="handleAdd"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:orders:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:orders:export']" icon="Download" plain type="warning" @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ordersList" @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="ID" prop="id" />
      <el-table-column align="center" label="购买人" prop="customer" />
      <el-table-column align="center" label="购买数量" prop="amount" />
      <el-table-column align="center" label="订单价格" prop="orderPrice" />
      <el-table-column align="center" label="单价" prop="unitPrice" />
      <el-table-column align="center" label="销售主体" prop="saler" />
      <el-table-column align="center" label="订单状态" prop="orderStatus">
        <template #default="scope">
          <dict-tag :options="task_status" :value="scope.row.orderStatus" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="日期" prop="createTime" />
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:orders:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['system:orders:remove']"
            icon="Delete"
            link
            type="primary"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改订单管理对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="500px">
      <el-form ref="ordersRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="购买人" prop="customer">
          <el-input v-model="form.customer" placeholder="请输入购买人" />
        </el-form-item>
        <el-form-item label="购买数量" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入购买数量" />
        </el-form-item>
        <el-form-item label="订单价格" prop="orderPrice">
          <el-input v-model="form.orderPrice" placeholder="请输入订单价格" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input v-model="form.unitPrice" placeholder="请输入单价" />
        </el-form-item>
        <el-form-item label="销售主体" prop="saler">
          <el-input v-model="form.saler" placeholder="请输入销售主体" />
        </el-form-item>
        <el-form-item label="日期" prop="createTime">
          <el-date-picker v-model="form.createTime" placeholder="请选择日期" type="date" value-format="YYYY-MM-DD" />
        </el-form-item>

        <el-form-item label="订单状态" prop="orderStatus">
          <el-radio-group v-model="form.orderStatus">
            <el-radio v-for="dict in task_status" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="Orders" setup>
import { listOrders, getOrders, delOrders, addOrders, updateOrders } from '@/api/system/orders';

const { proxy } = getCurrentInstance();
const { task_status } = proxy.useDict('task_status');

const ordersList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customer: null,
    amount: null,
    orderPrice: null,
    unitPrice: null,
    saler: null,
    orderStatus: null,
    createTime: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询订单管理列表 */
function getList() {
  loading.value = true;
  listOrders(queryParams.value).then((response) => {
    ordersList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    createTime: null,
    customer: null,
    amount: null,
    orderPrice: null,
    unitPrice: null,
    saler: null,
    orderStatus: null
  };
  proxy.resetForm('ordersRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加订单管理';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getOrders(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改订单管理';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['ordersRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateOrders(form.value).then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addOrders(form.value).then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除订单管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delOrders(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/orders/export',
    {
      ...queryParams.value
    },
    `orders_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
