<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="创建时间" prop="createDate">
        <el-date-picker
          clearable
          v-model="queryParams.createDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="基地ID" prop="baseId">
        <el-input v-model="queryParams.baseId" placeholder="请输入基地ID" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item label="基地名称" prop="baseName">
        <el-input v-model="queryParams.baseName" placeholder="请输入基地名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="上报设备" prop="deviceId">
        <el-input v-model="queryParams.deviceId" placeholder="请输入上报设备" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:weather:add']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:weather:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:weather:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="weatherList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="创建时间" align="center" prop="createDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="基地ID" align="center" prop="baseId" />
      <el-table-column label="温度" align="center" prop="wd" />
      <el-table-column label="湿度" align="center" prop="sd" />
      <el-table-column label="光照" align="center" prop="gz" />
      <el-table-column label="风向" align="center" prop="fx" />
      <el-table-column label="风力" align="center" prop="fl" />
      <el-table-column label="基地名称" align="center" prop="baseName" />
      <el-table-column label="上报设备" align="center" prop="deviceId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:weather:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:weather:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改气象管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="weatherRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="创建时间" prop="createDate">
          <el-date-picker
            clearable
            v-model="form.createDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="基地ID" prop="baseId">
          <el-input v-model="form.baseId" placeholder="请输入基地ID" />
        </el-form-item>
        <el-form-item label="温度" prop="wd">
          <el-input v-model="form.wd" placeholder="请输入温度" />
        </el-form-item>
        <el-form-item label="湿度" prop="sd">
          <el-input v-model="form.sd" placeholder="请输入湿度" />
        </el-form-item>
        <el-form-item label="光照" prop="gz">
          <el-input v-model="form.gz" placeholder="请输入光照" />
        </el-form-item>
        <el-form-item label="风向" prop="fx">
          <el-input v-model="form.fx" placeholder="请输入风向" />
        </el-form-item>
        <el-form-item label="风力" prop="fl">
          <el-input v-model="form.fl" placeholder="请输入风力" />
        </el-form-item>
        <el-form-item label="基地名称" prop="baseName">
          <el-input v-model="form.baseName" placeholder="请输入基地名称" />
        </el-form-item>
        <el-form-item label="上报设备" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入上报设备" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Weather">
import { listWeather, getWeather, delWeather, addWeather, updateWeather } from '@/api/system/weather';

const { proxy } = getCurrentInstance();

const weatherList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    createDate: null,
    baseId: null,
    wd: null,
    sd: null,
    gz: null,
    fx: null,
    fl: null,
    baseName: null,
    deviceId: null
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询气象管理列表 */
function getList() {
  loading.value = true;
  listWeather(queryParams.value).then((response) => {
    weatherList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    createDate: null,
    baseId: null,
    wd: null,
    sd: null,
    gz: null,
    fx: null,
    fl: null,
    baseName: null,
    deviceId: null
  };
  proxy.resetForm('weatherRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加气象管理';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getWeather(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改气象管理';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['weatherRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateWeather(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addWeather(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除气象管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delWeather(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/weather/export',
    {
      ...queryParams.value
    },
    `weather_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
