<!--suppress ALL -->
<template>
  <div class="pictureImgx">
    <!-- 按钮 -->
    <div class="floatingBtn float-animation" @click="openChatPanel">
      <img src="@/assets/images/Floating.gif" />
      <h2>参管家</h2>
    </div>
    <!-- 按钮 -->
    <!-- 聊天面板 -->
    <ChatPanel ref="chatPanel" :config="chatConfig" :showTriggerButton="false" />
    <!-- 标题 -->
    <ScrennHeader />
    <!-- 标题 -->
    <!-- 内容展示 -->
    <div class="viewport">
      <div class="columnLeft">
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>服务企业类型</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox" style="height: 180px" :style="{ marginTop: viewportHeight === 1080 ? '30px' : '0' }">
            <CircularChart :config="chartData.circularChartConfig" />
          </div>
        </div>
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>服务企业词云</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <WordChart :config="chartData.wordChartConfig" />
          </div>
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>企业信用评级</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <FunnelChart :config="chartData.funnelChartConfig" />
          </div>
        </div>
      </div>
      <div class="columnCenter">
        <div class="columnCenterTop" style="height: 66.5%">
          <div class="scrennThreeStyle">
            <ul class="scrennThreeData">
              <li>
                <div class="selection">
                  <h2>授信额度</h2>
                  <el-select
                    class="custom-select"
                    popper-class="custom-popper"
                    v-model="value"
                    placeholder="累计"
                    size="large"
                    style="width: 90px"
                  >
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <h3>
                  <!-- {{
                    value === '当日' ? Math.floor(allStatsConfig.todayAmount / 10000): Math.floor(allStatsConfig.allAmount / 10000)}}<i>亿元</i> -->
                  <NumberBox
                    v-if="value === '当日'"
                    :value="allStatsConfig.todayAmount"
                    unit="万"
                    background-color="#09ba86"
                    textColor="#09ba86"
                  />
                  <NumberBox
                    v-else
                    :value="allStatsConfig.allAmount"
                    unit="万"
                    background-color="#09ba86"
                    textColor="#09ba86"
                  />
                </h3>
              </li>
              <li>
                <div class="selection">
                  <h2>授信笔数</h2>
                  <el-select
                    class="custom-select"
                    popper-class="custom-popper"
                    v-model="valuex"
                    placeholder="累计"
                    size="large"
                    style="width: 90px"
                  >
                    <el-option v-for="item in optionsx" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <!-- <h3>{{ valuex === '当日' ? allStatsConfig.todayCount : allStatsConfig.allCount }}<i>笔</i></h3> -->
                <NumberBox
                  v-if="valuex === '当日'"
                  :value="allStatsConfig.todayCount"
                  unit="笔"
                  background-color="#1978e6"
                  textColor="#1978e6"
                />
                <NumberBox
                  v-else
                  :value="allStatsConfig.allCount"
                  unit="笔"
                  background-color="#1978e6"
                  textColor="#1978e6"
                />
              </li>
              <li>
                <div class="selection">
                  <h2>服务企业</h2>
                  <el-select
                    v-show="false"
                    class="custom-select"
                    popper-class="custom-popper"
                    v-model="valuexx"
                    placeholder=""
                    size="large"
                    style="width: 90px"
                  >
                    <el-option v-for="item in optionsxx" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <!-- <h3>{{ allStatsConfig.enterpriseCount }}<i>个</i></h3> -->
                <NumberBox v-if="valuexx === '当日'" :value="allStatsConfig.enterpriseCount" unit="个" />
                <NumberBox v-else :value="allStatsConfig.enterpriseCount" unit="个" />
              </li>
            </ul>
            <div class="scrennThreeEchart">
              <ul class="threeImageLeft">
                <li>
                  <img src="@/assets/images/u1330.png" class="float-animation" />
                  <div class="jirongText">
                    <p>{{ allStatsConfig1.cont2 }}<i>家</i></p>
                    <h2>种植主体</h2>
                  </div>
                </li>
                <li style="margin-top: 70px">
                  <img src="@/assets/images/u1333.png" class="float-animation" />
                  <div class="jirongText">
                    <p>{{ allStatsConfig1.cont3 }}<i>家</i></p>
                    <h2>加工企业</h2>
                  </div>
                </li>
              </ul>
              <div class="threeImageCenter" style="margin-top: 20px">
                <img src="@/assets/images/renshen02.png" class="float-animation" style="width: 120px; height: 261px" />
              </div>
              <ul class="threeImageRight">
                <li>
                  <img src="@/assets/images/u1331.png" class="float-animation" />
                  <div class="jirongText">
                    <p>{{ allStatsConfig1.cont5 }}<i>家</i></p>
                    <h2>销售企业</h2>
                  </div>
                </li>
                <li style="margin-top: 70px">
                  <img src="@/assets/images/u1332.png" class="float-animation" />
                  <div class="jirongText">
                    <p>{{ allStatsConfig.financialCount }}<i>家</i></p>
                    <h2>金融机构</h2>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="columnCenterBottom">
          <div class="centerItem">
            <div class="boxTitlex">
              <div class="Title">
                <h2><i></i>融资授信趋势</h2>
                <ul class="tablist">
                  <li
                    v-for="(tab, index) in tabs"
                    :key="index"
                    @click="onActiveIndex(index)"
                    :class="{ active: activeIndex === index }"
                  >
                    {{ tab }}
                  </li>
                </ul>
              </div>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <LineBar :config="chartData.lineBarConfig" />
            </div>
          </div>
          <div class="centerItem">
            <div class="boxTitle">
              <h2><i></i>金融产品分布</h2>
              <img src="@/assets/images/title_line.png" />
            </div>
            <div class="contentBox">
              <LineBar2 :config="chartData.lineBar2Config" />
            </div>
          </div>
        </div>
      </div>
      <div class="columnRight">
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>产业授信分布</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <RoseChart :config="chartData.roseChartConfig" />
          </div>
        </div>
        <div class="columnItem">
          <div class="boxTitle">
            <h2><i></i>贷后预警提示</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <auto-scroll-table :data="dataList02" :scroll-delay="30" @custom-event="handleRowClick">
              <el-table-column label="产区名称" align="center">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.enterpriseName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" align="center">
                <template v-slot="scope">
                  <span :class="getLevelClass(scope.row.enterpriseType)">{{ scope.row.enterpriseType }}</span>
                </template>
              </el-table-column>
              <el-table-column label="内容" align="center">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.tips }}</span>
                </template>
              </el-table-column>
            </auto-scroll-table>
          </div>
        </div>
        <div class="columnItem" style="margin-bottom: 0">
          <div class="boxTitle">
            <h2><i></i>银行放款排名</h2>
            <img src="@/assets/images/title_line.png" />
          </div>
          <div class="contentBox">
            <sort-table
              :style="{ height: tableHeight, transition: 'height 0.3s ease-in-out' }"
              :data="dataList01"
              :scroll-delay="50"
              @custom-event="handleRowClick02"
            >
              <el-table-column label="排名" align="center">
                <template v-slot="scope">
                  <span v-if="scope.row.index == 1">
                    <img src="@/assets/images/one.png" height="20px" />
                  </span>
                  <span v-else-if="scope.row.index == 2">
                    <img src="@/assets/images/two.png" height="20px" />
                  </span>
                  <span v-else-if="scope.row.index == 3">
                    <img src="@/assets/images/three.png" height="20px" />
                  </span>
                  <span v-else>
                    {{ scope.row.index }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="银行名称" align="center">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.bank_name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="放款笔数" align="center">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.lending_count }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="放款金额(万元)" align="center" width="150">
                <template v-slot="scope">
                  <span class="customSpan">{{ scope.row.total_amount }}</span>
                </template>
              </el-table-column>
            </sort-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 内容展示 -->
    <!-- 弹框 -->
    <el-dialog v-model="dialogVisible" title="贷后预警提示详情" width="600" class="blue-dialog">
      <ul class="visibleBox">
        <li>
          <h2>产区</h2>
          <h3>{{ visibleNr.enterpriseName }}</h3>
        </li>
        <li>
          <h2>类型</h2>
          <h3 :class="getLevelClass(visibleNr.enterpriseType)">{{ visibleNr.enterpriseType }}</h3>
        </li>
        <li>
          <h2>内容</h2>
          <h3>{{ visibleNr.tips }}</h3>
        </li>
      </ul>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" class="sureBtn">确定</el-button>
          <el-button type="primary" @click="dialogVisible = false" class="resetBtn">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹框 -->
    <!-- 弹框 -->
    <el-dialog v-model="dialogVisiblex" title="银行放款详情" width="600" class="blue-dialog">
      <ul class="visibleBox">
        <li>
          <h2>银行名称</h2>
          <h3>{{ visibleNrx.bank_name }}</h3>
        </li>
        <li>
          <h2>放款笔数</h2>
          <h3>{{ visibleNrx.lending_count }}</h3>
        </li>
        <li>
          <h2>放款金额</h2>
          <h3>{{ visibleNrx.total_amount }}万元</h3>
        </li>
      </ul>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisiblex = false" class="sureBtn">确定</el-button>
          <el-button type="primary" @click="dialogVisiblex = false" class="resetBtn">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹框 -->
    <!-- 边框线 -->
    <div class="pictureSix"></div>
    <!-- 边框线 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ScrennHeader from '@/components/ScrennHeader';
import AutoScrollTable from '@/components/AutoScrollTable';
import SortTable from '@/components/SortTable';
import LineBar from './components/LineBar.vue';
import LineBar2 from './components/LineBar2.vue';
import CircularChart from './components/CircularChart.vue';
import RoseChart from './components/RoseChart.vue';
import FunnelChart from './components/FunnelChart.vue';
import WordChart from './components/WordChart.vue';
import ChatPanel from '../../components/ChatPanel/index.vue';
import NumberBox from './components/NumberBox.vue';
import {
  getEnterpriseTypes,
  getEnterpriseWordCloud,
  getCreditRating,
  getFinancingTrend,
  getProductCreditDistribution,
  getBankLoanRanking,
  getAllStats,
  getRiskWarningTips
} from '@/api/screen/index3';
//tab
const tabs = ref(['年', '月']);
const activeIndex = ref(0);
//select
const value = ref('累计');
const options = [
  {
    value: '累计',
    label: '累计'
  },
  {
    value: '当日',
    label: '当日'
  }
];
const valuex = ref('累计');
const optionsx = [
  {
    value: '累计',
    label: '累计'
  },
  {
    value: '当日',
    label: '当日'
  }
];
const valuexx = ref('累计');
const optionsxx = [
  {
    value: '累计',
    label: ''
  },
  {
    value: '当日',
    label: ''
  }
];
//页面高度
const viewportHeight = ref(0);
const updateViewportHeight = () => {
  viewportHeight.value = window.innerHeight;
};
// 弹框样式
const dialogVisible = ref(false);
const dialogVisiblex = ref(false);
//弹框内容
const visibleNr = ref({});
const visibleNrx = ref({});
//数据
// 监听一下数据
const dataList01 = ref([]);
const tableHeight = ref('100%');

//数据处理
// const processedNumberParts = computed(() => {
//   // 将数值转为字符串，补零到 6 位，保证格式统一，比如 230 会变成 000230
//   const strValue = props.value.toString().padStart(6, '0');
//   // 分割字符串，在第 3 位后插入逗号，得到 ['000', ',230'] 这样的数组（这里简单处理，可扩展更通用逻辑）
//   return [strValue.slice(0, 3), ',' + strValue.slice(3)];
// });
watch(
  dataList01,
  (newVal) => {
    // 数据更新时改变高度
    tableHeight.value = '99%';

    dataList01.value = newVal;
  },
  { deep: true }
);

const dataList02 = ref([
  { index: 1, name: '产区作业1', type: '2025/05/12', content: '3小时' },
  { index: 2, name: '产区作业2', type: '2025/05/12', content: '3小时' },
  { index: 3, name: '产区作业3', type: '2025/05/12', content: '3小时' },
  { index: 4, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 5, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 6, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 7, name: '产区作业', type: '2025/05/12', content: '3小时' },
  { index: 8, name: '产区作业', type: '2025/05/12', content: '3小时' }
]);
const chartData = ref({
  circularChartConfig: {
    data: []
  },
  wordChartConfig: {
    data: []
  },
  funnelChartConfig: {
    data: []
  },
  lineBarConfig: {
    data: {
      YData: [],
      XData: []
    }
  },
  lineBar2Config: {
    data: {}
  },
  roseChartConfig: {
    data: []
  }
});
// ChatPanel 配置
const chatConfig = ref({
  ip: '************',
  title: '参管家',
  wsPort: 8282,
  httpPort: 8383,
  showTriggerButton: false
});
// 监听表格单元格点击（Element Plus 原生事件）
const handleRowClick = (row) => {
  dialogVisible.value = true;
  visibleNr.value = row;
};
const handleRowClick02 = (row) => {
  dialogVisiblex.value = true;
  visibleNrx.value = row;
};

// 获取组件引用
const chatPanel = ref(null);

//融资授信趋势 接口调用
const getRzsx = async () => {
  const res = await getFinancingTrend();
  chartData.value.lineBarConfig.data.XData = res.data.yearList;
  chartData.value.lineBarConfig.data.YData = res.data.yearAmountList.map((item) => {
    return item.endsWith('.00') ? item.slice(0, -3) : item;
  });
};
const onActiveIndex = async (index) => {
  activeIndex.value = index;
  const res = await getFinancingTrend();
  if (index == 0) {
    chartData.value.lineBarConfig.data.XData = res.data.yearList;
    chartData.value.lineBarConfig.data.YData = res.data.yearAmountList.map((item) => {
      return item.endsWith('.00') ? item.slice(0, -3) : item;
    });
  } else {
    chartData.value.lineBarConfig.data.XData = res.data.monthList;
    chartData.value.lineBarConfig.data.YData = res.data.amountList.map((item) => {
      return item.endsWith('.00') ? item.slice(0, -3) : item;
    });
  }
};
const allStatsConfig1 = ref({
  cont1: 0,
  cont2: 0,
  cont3: 0,
  cont4: 0,
  cont5: 0
});
const allStatsConfig = ref({
  allAmount: 0,
  allCount: 0,
  enterpriseCount: 0,
  financialCount: 0,
  plantCount: 0,
  processCount: 0,
  saleCount: 0,
  todayAmount: 0,
  todayCount: 0
});
// 打开面板方法
const openChatPanel = () => {
  chatPanel.value?.openPanel();
};

let timer = null;

const getList = async () => {
  const res = await getEnterpriseTypes();
  if (res.code === 200) {
    chartData.value.circularChartConfig.data = res.data;
  }

  const res3 = await getCreditRating();
  if (res3.code === 200) {
    chartData.value.funnelChartConfig.data = res3.data;
  }
  // const res4 = await getFinancingTrend();
  // if (res4.code === 200) {
  //   res4.data.amountList = res4.data.amountList.map((item) => {
  //     return Math.round(item);
  //   });
  //   chartData.value.lineBarConfig.data = res4.data;
  // }
  const res5 = await getProductCreditDistribution();
  if (res5.code === 200) {
    chartData.value.lineBar2Config.data = res5.data;
    chartData.value.roseChartConfig.data = res5.data;
  }
  const res6 = await getBankLoanRanking();
  if (res6.code === 200) {
    dataList01.value = res6.data;
  }

  const res7 = await getAllStats();
  if (res7.code === 200) {
    allStatsConfig.value = res7.data;

    // enterpriseCount
    res7.data.salesEntityCountByType.forEach((item) => {
      if (item.type_name === '种源') {
        allStatsConfig1.value.cont1 = item.enterprise_count;
      }
      if (item.type_name === '种植') {
        allStatsConfig1.value.cont2 = item.enterprise_count;
      }
      if (item.type_name === '加工') {
        allStatsConfig1.value.cont3 = item.enterprise_count;
      }
      if (item.type_name === '检定') {
        allStatsConfig1.value.cont4 = item.enterprise_count;
      }
      if (item.type_name === '销售') {
        allStatsConfig1.value.cont5 = item.enterprise_count;
      }
    });
  }

  const res8 = await getRiskWarningTips();
  if (res8.code === 200) {
    dataList02.value = res8.data;
  }
};
const getLevelClass = (level) => {
  switch (level) {
    case '加工企业':
      return 'risk-level-low';
    case '种植主体':
      return 'risk-level-medium';
    case '高风险':
      return 'risk-level-high';
    default:
      return 'risk-level-default';
  }
};
const cty = async () => {
  const res2 = await getEnterpriseWordCloud();
  if (res2.code === 200) {
    chartData.value.wordChartConfig.data = res2.data.map((item) => ({
      name: item.name,
      value: item.id
    }));
  }
};
onMounted(async () => {
  getList();
  getRzsx();
  cty();
  // timer = setInterval(() => {
  //   getList();
  // }, 3000);
  updateViewportHeight(); // 初始化视口高度
  window.addEventListener('resize', updateViewportHeight); // 监听窗口大小变化
});
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  window.removeEventListener('resize', updateViewportHeight); // 移除监听
});
</script>
<style scoped>
/* 控制选择器本身样式 */
.custom-select {
  /* 使选择器背景透明，可根据实际需求调整 */
  --el-select-input-background-color: transparent;
  --el-select-input-border-color: transparent;
  --el-select-input-hover-border-color: transparent;
  --el-select-input-focus-border-color: transparent;
  /* 文字颜色等也可自定义 */
  --el-select-input-color: #fff;
}

/* 去掉默认的箭头图标，也可以自定义替换成自己的箭头 */
.custom-select .el-input__suffix {
  display: none;
}
.custom-select :deep(.el-select__wrapper) {
  background-color: transparent;
  box-shadow: none;
}
.custom-select :deep(.el-select__placeholder) {
  color: #fff;
}
.custom-select .el-select__wrapper {
  background-color: #74d5ff !important;
}
/* 控制下拉弹窗样式 */
.custom-popper {
  /* 背景透明或设置成你想要的背景图等，这里模拟科技感用透明加边框 */
  background-color: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.5);
  border-radius: 8px;
  /* 调整下拉弹窗的偏移等，根据实际布局微调 */
  margin-top: 8px;
}

/* 下拉选项样式 */
.custom-popper .el-select-dropdown__item {
  color: #333;
  background-color: transparent;
  /*  hover 状态样式 */
  &:hover {
    background-color: rgba(30, 144, 255, 0.2);
  }
  /* 选中状态样式 */
  &.selected {
    background-color: rgba(30, 144, 255, 0.3);
    color: #fff;
  }
}

.risk-level-low {
  color: #1978e6;
}
.risk-level-medium {
  color: #20bc8e;
}
.risk-level-high {
  color: #e9cc10;
}
.risk-level-default {
  color: #74d5ff;
}
</style>
