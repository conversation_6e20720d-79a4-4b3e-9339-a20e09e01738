<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: {
        YData: [],
        XData: []
      }
    })
  }
});
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

// 初始化图表
const initChart = () => {
  const data = props.config.data || {};
  const XData = data.XData || [];
  const YData = data.YData || [];

  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  chartInstance = echarts.init(chartContainer.value);
  
  // 图表配置
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)',
      borderColor: 'rgba(80,180,255,0.5)',
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: (params) => `${params[0].name}<br/>授信额度: ${params[0].value} 万元`
    },
    grid: {
      top: '15%',
      left: '5%',  // 减小左侧边距，为X轴标签腾出更多空间
      right: '5%', // 减小右侧边距
      bottom: '5%', // 增加底部边距，避免标签被截断
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: XData,
      axisLine: { lineStyle: { color: '#74D5FF' } },
      axisTick: { show: false },
      axisLabel: {
        color: '#74D5FF',
        fontSize: 10,  // 减小字体大小，使更多标签能显示
        // rotate: 4,    // 旋转标签45度，防止水平方向拥挤
        interval: 0,   // 强制显示所有标签（默认会自动隐藏部分标签）
        showMinLabel: true, // 显示最小刻度标签
        showMaxLabel: true, // 显示最大刻度标签
        // 为长文本添加换行（可选）
        // formatter: function (value) {
        //   return value.split('').join('\n');
        // }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '单位：万元',
        nameTextStyle: { color: '#74D5FF', fontSize: 12, align: 'right' },
        splitLine: { lineStyle: { type: 'dashed', color: 'rgba(116, 213, 255, 0.2)' } },
        axisLine: { lineStyle: { color: '#74D5FF' } },
        axisLabel: { color: '#74D5FF', fontSize: 12 }
      }
    ],
    series: [
      {
        name: '授信额度',
        type: 'bar',
        label: {
        show: true, // 开启显示数值标签
        position: 'top', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
        textStyle: {
          color: '#20bc8e', // 标签文字颜色，可按需调整
          fontSize: 12 // 标签文字大小，可按需调整
        }
      },
        barWidth: 20,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: '#68eec6' },
            { offset: 1, color: '#20bc8e' }
          ])
        },
        data: YData,
        z: 1
      }
    ]
  };

  chartInstance.setOption(option);
};

// 重新加载图表
const reloadChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = echarts.init(chartContainer.value);
    initChart();
    console.log('图表已重新加载');
  }
};

// 调整图表尺寸
const resizeChart = () => {
  chartInstance?.resize();
};

// 窗口大小变化处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 监听数据变化
watch(
  () => props.config.data,
  (newVal) => {
    if (newVal) {
      nextTick(initChart);
    }
  },
  { deep: true }
);

onMounted(() => {
  nextTick(() => {
    chartInstance = echarts.init(chartContainer.value);
    initChart();
    window.addEventListener('resize', handleResize);
    reloadTimer = setInterval(reloadChart, 18000);
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  if (reloadTimer) {
    clearInterval(reloadTimer);
    reloadTimer = null;
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style lang="scss" scoped>
.w-full { width: 100%; }
.h-full { height: 100%; }
</style>