<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});

// 监听数据变化
watch(
  () => props.config.data,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      nextTick(() => {
        initChart();
      });
    }
  },
  { deep: true }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 计算数据总和（用于后续显示占比说明）
  const total = props.config.data.reduce((sum, item) => sum + item.value, 0);

  // 图表配置，使用原始数值显示
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)',
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: (params) => {
        // 提示框显示原始数值+占比（可选）
        return `${params.name}：${params.value}个\n占比：${((params.value / total) * 100).toFixed(1)}%`;
      }
    },
    series: [
      {
        name: '数量',
        type: 'pie',
        radius: ['50%', '70%'],
        data: props.config.data,
        label: {
          show: true,
          color: '#74D5FF',
          // 标签直接显示原始数值
          formatter: '{b}: {c}个'
        },
        itemStyle: {
          normal: {
            color: function (params) {
              var colorList = [
                { c1: '#19d9fe', c2: '#19d9fe' },
                { c1: '#1978e6', c2: '#1978e6' },
                { c1: '#20bc8e', c2: '#20bc8e' },
                { c1: '#e9cc10', c2: '#e9cc10' }
              ];
              return new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                { offset: 0, color: colorList[params.dataIndex].c1 },
                { offset: 1, color: colorList[params.dataIndex].c2 }
              ]);
            }
          }
        }
      }
    ],
    graphic: {
      elements: [
        {
          type: 'image',
          z: 100,
          left: '38.3%',
          top: 'center',
          style: {
            image: new URL('/src/assets/images/rs.png', import.meta.url).href,
            width: 96,
            height: 96,
            borderRadius: '50%'
          }
        }
      ]
    }
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  startReloadTimer();
  initChart();
});

// 设置定时器重新加载图表
const startReloadTimer = () => {
  setInterval(initChart, 10000);
};
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>
