<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
//监听数据
watch(
  () => props.config.data,
  (newVal) => {
    initChart();
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;
let reloadCount = 0; // 记录重新加载次数

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置，使用保存的初始数据
  const option = {
    color: ['#20bc8e', '#e9cc10', '#a24747', '#6b47a2'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },

      formatter: (params) => {
        return `${params[0].name}: ${params[0].value}笔`;
      }
    },
    legend: {
      orient: 'horizontal',
      right: 10,
      top: 10,
      textStyle: {
        color: '#74D5FF'
      },
      itemWidth: 12,
      itemHeight: 12
    },
    grid: {
      left: '5%',
      right: '4%',
      bottom: '0%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#74D5FF'
        }
      },

      data: props.config.data.creditNameList
    },
    yAxis: {
      type: 'value',
      name: '单位：笔',
      nameTextStyle: {
        color: '#74D5FF',
        fontSize: 12,
        align: 'right'
      },
      axisLine: {
        lineStyle: {
          color: '#74D5FF'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(116, 213, 255, 0.1)'
        }
      },
      axisLabel: {
        color: '#74D5FF'
      }
    },
    series: [
      {
        name: '产业授信分布',
        type: 'line',
        symbol: 'circle',
        symbolSize: 8,
        data: props.config.data.creditPieceList,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          opacity: 0.1
        }
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
  reloadCount++;
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表（可根据需要调整）
  reloadTimer = setInterval(initChart, 14000);
};

onMounted(() => {
  nextTick(() => {
    initChart(); // 首次初始化
    resizeChart();

    window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时器
  });
});

onBeforeUnmount(() => {
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
});

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>
