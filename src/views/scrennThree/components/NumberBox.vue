<template>
  <div class="number-display-container">
    <div 
      v-for="(char, index) in formattedNumberChars" 
        :style="{ backgroundColor: props.backgroundColor}"
      :key="index" 
      :class="[
        'digit-box', 
        char === ',' ? 'separator' : '', 
        index === formattedNumberChars.length - 1 ? 'last-digit' : ''
      ]"
    >
      {{ char }}
    </div>
    <div v-if="props.unit" class="unit" :style="{color: props.textColor,marginTop:props.textMarigin }">{{ props.unit }}</div>
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue';

const props = defineProps({
  value: {
    type: Number,
    required: true
  },
  unit: {
    type: String,
    default: ''
  },
  digits: {
    type: Number,
    default: 6
  },
  separatorPosition: {
    type: Number,
    default: 3
  },
  backgroundColor: {
    type: String,
     default: '#00b8d4'
  },
  textColor: {
    type: String,
    default: '#00b8d4'
  },
  textMarigin:{
    type: String,
    default: '20px'
  }
});

// 处理数字，拆分为字符数组，包含分隔符
const formattedNumberChars = computed(() => {
  // 补零到指定长度
  const paddedValue = props.value.toString().padStart(props.digits, '0');
  
  // 按指定位置插入分隔符
  const parts = [];
  for (let i = 0; i < paddedValue.length; i++) {
    if (i === props.separatorPosition) {
      parts.push(',');
    }
    parts.push(paddedValue[i]);
  }
  
  return parts;
});
</script>

<style scoped>
.number-display-container {
    display: flex;
    font-size: 24px;
    color: #fff;

}
.digit-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 40px;
  margin: 0 1px;
  border-radius: 2px;
  transition: all 0.2s;
  font-weight: bold;
}

.separator {
  width: 10px;
  border: none;
  background: none !important;
}

.last-digit {
  margin-right: 4px;
}

.unit {
  margin-left: 6px;
  font-size: 12px;
}
</style>
    