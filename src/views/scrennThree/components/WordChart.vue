<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import 'echarts-wordcloud';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
// 监听数据变化
watch(
  () => props.config.data,
  (newVal) => {
    console.log('%c [ newVal ]-23', 'font-size:13px; background:pink; color:#bf2c9f;', newVal);
    initChart();
  },
  { deep: true }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 确保数据格式正确
  // 过滤和排序数据：优先显示公司名称，限制人名显示
  const companies = props.config.data.filter((item) => {
    const name = item.name || '';
    return (
      name.includes('公司') ||
      name.includes('集团') ||
      name.includes('企业') ||
      name.includes('有限') ||
      name.includes('股份') ||
      name.includes('科技') ||
      name.includes('农业') ||
      name.includes('种植') ||
      name.includes('基地') ||
      name.includes('合作社') ||
      name.includes('农场') ||
      name.includes('基地')
    );
  });

  const shortNames = props.config.data.filter((item) => {
    const name = item.name || '';
    // 简单识别人名：2-4个字，且不包含明显的公司关键词
    return (
      name.length <= 4 &&
      name.length >= 2 &&
      !name.includes('公司') &&
      !name.includes('集团') &&
      !name.includes('企业') &&
      !name.includes('有限') &&
      !name.includes('股份') &&
      !name.includes('科技') &&
      !name.includes('农业') &&
      !name.includes('种植') &&
      !name.includes('基地') &&
      !name.includes('合作社') &&
      !name.includes('农场')
    );
  });

  const otherNames = props.config.data.filter((item) => {
    const name = item.name || '';
    // 其他长名称，但不包含公司关键词
    return (
      name.length > 4 &&
      !name.includes('公司') &&
      !name.includes('集团') &&
      !name.includes('企业') &&
      !name.includes('有限') &&
      !name.includes('股份') &&
      !name.includes('科技') &&
      !name.includes('农业') &&
      !name.includes('种植') &&
      !name.includes('基地') &&
      !name.includes('合作社') &&
      !name.includes('农场')
    );
  });

  // 随机打乱数据顺序的函数
  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // 按优先级组合：公司名称 + 少量人名 + 其他名称，并随机打乱
  const sortedCompanies = shuffleArray(companies.sort((a, b) => (b.value || 0) - (a.value || 0))).slice(0, 8); // 限制公司名称最多8个
  const sortedShortNames = shuffleArray(shortNames.sort((a, b) => (b.value || 0) - (a.value || 0))).slice(0, 5); // 取前5个人名
  const sortedOtherNames = shuffleArray(otherNames.sort((a, b) => (b.value || 0) - (a.value || 0))).slice(0, 2); // 其他名称最多2个

  // 组合数据，确保人名和公司都能显示，并再次随机打乱
  const sortedData = shuffleArray([...sortedCompanies, ...sortedShortNames, ...sortedOtherNames]);

  const processedData = sortedData.map((item) => ({
    name: item.name || '',
    value: item.value || 0,
    textStyle: {
      color: getRandomColor()
    }
  }));

  // 图表配置
  const option = {
    tooltip: {
      show: true,
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: function (params) {
        return `${params.data.name}: ${params.data.value}`;
      }
    },
    series: [
      {
        type: 'wordCloud', //类型是词云图
        shape: 'circle', //词云图的形状为圆形
        keepAspect: false, //不保持词云图的纵横比
        //词云图的位置和大小
        left: 'center',
        top: 'center',
        width: '100%',
        height: '100%',
        right: null,
        bottom: null,
        sizeRange: [15, 25], //词云图中词语的字体大小范围，最小12px，最大60px
        rotationRange: [0, 0], //词云图中词语的旋转角度范围
        gridSize: 10, //词云图中词语的间距
        drawOutOfBound: false, //不绘制超出词云图范围的词语
        layoutAnimation: true, //开启布局动画
        //词云图中词语的样式
        textStyle: {
          fontWeight: 'bold',
          color: function () {
            return (
              'rgb(' +
              [Math.round(Math.random() * 160), Math.round(Math.random() * 160), Math.round(Math.random() * 160)].join(
                ','
              ) +
              ')'
            );
          }
        },
        // 词云图中词语的高亮样式
        emphasis: {
          textStyle: {
            textShadowBlur: 2,
            textShadowColor: 'orange'
          }
        },
        data: processedData
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
};

// 设置定时器重新加载图表
const startReloadTimer = () => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  // 每3秒重新加载一次图表
  reloadTimer = setInterval(initChart, 13000);
};

// 生成随机颜色
const getRandomColor = () => {
  const colors = ['#1890FF', '#36CBCB', '#4ECB73', '#FBD437', '#F2637B', '#975FE5', '#36CBCB', '#4ECB73'];
  return colors[Math.floor(Math.random() * colors.length)];
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  // window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  initChart();
  nextTick(() => {
    // initChart();
    // window.addEventListener('resize', handleResize);
    startReloadTimer(); // 启动定时器
  });
});

onBeforeUnmount(() => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  window.removeEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  if (reloadTimer) {
    clearInterval(reloadTimer);
  }
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
