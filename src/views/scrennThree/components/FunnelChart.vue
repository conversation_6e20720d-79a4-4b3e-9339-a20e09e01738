<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: []
    })
  }
});
// 监听数据变化
watch(
  () => props.config.data,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      nextTick(() => {
        initChart();
      });
    }
  },
  { deep: true }
);
const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 确保数据格式正确
  const processedData = props.config.data.map((item) => ({
    value: item.value || 0,
    name: item.name || ''
  }));

  // 获取所有名称用于图例
  const legendData = processedData.map((item) => item.name);

  // 图表配置
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    // legend: {
    //   data: legendData,
    //   bottom: '5%',
    //   left: 'center',
    //   textStyle: {
    //     color: '#74D5FF'
    //   },
    //   itemWidth: 12,
    //   itemHeight: 12,
    //   itemGap: 20
    // },
    series: [
      {
        name: '企业信用评级',
        type: 'funnel',
        left: '15%',
        top: 20,
        bottom: 10,
        min: 0,
        max: Math.max(...processedData.map((item) => item.value)) || 100,
        minSize: '20%',
        maxSize: '100%',
        sort: 'none',
        gap: 2,
        label: {
          show: true,
          position: 'right',
          formatter: '{b}: {c}',
          fontSize: 14,
          color: '#74D5FF',
          distance: 15
        },
        labelLine: {
          show: true,
          length: 20,
          length2: 30,
          lineStyle: {
            width: 1,
            type: 'solid',
            color: '#74D5FF'
          }
        },
        emphasis: {
          label: {
            fontSize: 16,
            color: '#fff'
          }
        },
        itemStyle: {
          normal: {
            borderColor: 'rgba(255,255,255,0.2)',
            borderWidth: 1,
            color: function (params) {
              const colors = ['#f2637b', '#e9cc10', '#20bc8e', '#1978e6', '#19d9fe', '#975fe5'];
              return colors[params.dataIndex % colors.length];
            }
          }
        },
        data: processedData
      }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  initChart();
  setInterval(initChart, 15000);
});

onBeforeUnmount(() => {
  if (reloadTimer) {
    clearInterval(reloadTimer);
    reloadTimer = null;
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
  clearTimeout(window.resizeTimeout);

  // 确保定时器被清理
  if (reloadTimer) {
    clearInterval(reloadTimer);
    reloadTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
