<template>
  <div ref="chartContainer" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      data: {
        creditAmountList: [],
        creditNameList: [],
        creditPieceList: []
      }
    })
  }
});
// 监听数据变化
watch(
  () => props.config.data,
  (newVal) => {
    if (newVal) {
      initChart();
    }
  },
  { deep: true }
);

const chartContainer = ref(null);
let chartInstance = null;
let reloadTimer = null;

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 销毁可能存在的旧实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);

  // 图表配置
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(20,40,80,0.85)', // 科技风半透明深色
      borderColor: 'rgba(80,180,255,0.5)', // 可选：边框带点蓝色科技感
      borderWidth: 1,
      textStyle: {
        color: '#74D5FF',
        fontSize: 14
      },
      formatter: function (params) {
        let result = params[0].name + '<br/>';
        params.forEach((item) => {
          if (item.seriesName === '授信额度') {
            result += `${item.seriesName}: ${item.value} 万元<br/>`;
          } else {
            result += `${item.seriesName}: ${item.value} 笔<br/>`;
          }
          // if (item.seriesName === '授信额度') {
          //   result += `同比: ${((item.value / 10000) * 100).toFixed(2)}%<br/>`;
          // }
        });
        return result;
      }
    },
    grid: {
      top: '20%',
      left: '5%',
      right: '5%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.config.data.creditNameList,
      axisLine: {
        lineStyle: {
          color: '#74D5FF'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#74D5FF',
        fontSize: 12
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '单位：万元',
        nameTextStyle: {
          color: '#74D5FF',
          fontSize: 12,
          align: 'right'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(116, 213, 255, 0.2)'
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#74D5FF'
          }
        },
        axisLabel: {
          color: '#74D5FF',
          fontSize: 12
        }
      }
      // {
      //   type: 'value',
      //   name: '单位：笔',
      //   nameTextStyle: {
      //     color: '#74D5FF',
      //     fontSize: 12,
      //     align: 'left'
      //   },
      //   splitLine: {
      //     show: false
      //   },
      //   axisLine: {
      //     show: true,
      //     lineStyle: {
      //       color: '#74D5FF'
      //     }
      //   },
      //   axisLabel: {
      //     color: '#74D5FF',
      //     fontSize: 12
      //   }
      // }
    ],
    series: [
      {
        name: '授信额度',
        type: 'bar',
           label: {
        show: true, // 开启显示数值标签
        position: 'top', // 标签显示在柱状图顶部，也可根据需求改为 'inside'（内部）、'bottom'（底部）等
        textStyle: {
          color: '#f7cd3a', // 标签文字颜色，可按需调整
          fontSize: 12 // 标签文字大小，可按需调整
        }
      },
        barWidth: 20,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
              offset: 0,
              color: '#f7cd3a'
            },
            {
              offset: 1,
              color: '#f7793f'
            }
          ])
        },
        data: props.config.data.creditAmountList
      }
      // {
      //   name: '授信笔数',
      //   type: 'line',
      //   yAxisIndex: 1,
      //   symbol: 'circle',
      //   symbolSize: 8,
      //   itemStyle: {
      //     color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      //       {
      //         offset: 0,
      //         color: '#19d9fe'
      //       },
      //       {
      //         offset: 0.5,
      //         color: '#2e95ff'
      //       },
      //       {
      //         offset: 1,
      //         color: '#19d9fe'
      //       }
      //     ])
      //   },
      //   lineStyle: {
      //     width: 2,
      //     color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      //       {
      //         offset: 0,
      //         color: '#19d9fe'
      //       },
      //       {
      //         offset: 0.5,
      //         color: '#2e95ff'
      //       },
      //       {
      //         offset: 1,
      //         color: '#19d9fe'
      //       }
      //     ])
      //   },
      //   data: props.config.data.creditPieceList
      // }
    ]
  };

  // 设置配置
  chartInstance.setOption(option);
};

// 调整图表尺寸
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 窗口大小变化事件处理
const handleResize = () => {
  clearTimeout(window.resizeTimeout);
  window.resizeTimeout = setTimeout(resizeChart, 100);
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
  setInterval(initChart, 18000);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 清理定时器
  if (reloadTimer) {
    clearInterval(reloadTimer);
    reloadTimer = null;
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  // 确保定时器被清理
  if (reloadTimer) {
    clearInterval(reloadTimer);
    reloadTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
