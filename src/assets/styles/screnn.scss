// 重置样式
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 14px;
}
body {
  height: 100%;
  color: #333;
  font-family:
    'PingFangSC-Regular', 'PingFang SC', 'Avenir', 'Segoe UI', 'Hiragino Sans GB', 'STHeiti', 'Microsoft Sans Serif',
    'WenQuanYi Micro Hei', sans-serif !important;
}
body,
ul,
h1,
h3,
h4,
p,
dl,
dd {
  padding: 0;
  margin: 0;
}
a {
  text-decoration: none;
  color: #333;
  outline: none;
}
i {
  font-style: normal;
}
input[type='text'],
input[type='search'],
input[type='password'],
input[type='checkbox'] {
  padding: 0;
  outline: none;
  border: none;
  -webkit-appearance: none;
  &::placeholder {
    color: #ccc;
  }
}
img {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
}
ul {
  list-style: none;
}

#app {
  user-select: none;
}

.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ellipsis-2 {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:after {
  content: '.';
  display: block;
  visibility: hidden;
  height: 0;
  line-height: 0;
  clear: both;
}
// 阿里字体图标设置
.icon,
.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

//背景图片
.pictureImg {
  min-height: 100vh;
  width: 100%;
  background: url('@/assets/images/bg.jpg') no-repeat;
  background-size: 100% 100%;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  pointer-events: auto;
}
.pictureImgxx {
  min-height: 100vh;
  width: 100%;
  background: url('@/assets/images/bgxxx.gif') no-repeat;
  background-size: 100% 100%;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  pointer-events: auto;
}
.pictureImgx {
  min-height: 100vh;
  width: 100%;
  background: url('@/assets/images/bgjr.gif') no-repeat;
  background-size: 100% 100%;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}
.pictureSix {
  min-height: 100vh;
  width: 100%;
  background: url('@/assets/images/sixBg.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  pointer-events: none;
}
.viewport {
  width: 96%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
  top: 105px;
}
.columnLeft {
  width: 24%;
}
.columnCenter {
  width: 50%;
}
.columnRight {
  width: 24%;
}
.columnItem {
  width: 100%;
  height: 32.6%;
  margin-bottom: 2.3%;
  position: relative;
  background: url('@/assets/images/box.png') no-repeat;
  background-size: 100% 100%;
}

.columnItemxx {
  background: url('@/assets/images/box1.png') no-repeat;
  background-size: 100% 100%;
}
.columnItemFive {
  background: url('@/assets/images/u72.png') no-repeat;
  background-size: 100% 100%;
}
.columnCenterTop {
  width: 100%;
  height: 66.5%;
  position: relative;
}
.imgbox {
  width: 40%;
  height: 72%;
  margin: 0 auto;
}
.imgTop {
  width: 100%;
  height: 100%;
  padding-top: 10%;
  position: relative;
  // .square {
  //   width: 100%;
  //   position: absolute;
  //   top: 21.5%;
  //   left: 12.1%;
  // }
  .renshen {
    position: absolute;
    width: 80px;
    top: 59%;
    left: 50%;
    transform: translate(-50%, -50%); /* 移动自身的一半宽度和高度 */
  }
}
.imgBottom {
  width: 100%;
  height: 28%;
  img {
    width: 100%;
  }
}
.iconBox {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  li {
    width: 150px;
    height: 33.3%;
    position: relative;
    img {
      width: 150px;
      height: 150px;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 8;
    }
    .text {
      width: 100%;
      position: absolute;
      left: 0;
      top: 10%;
      z-index: 9;
      text-align: center;
      h2 {
        color: #e9cc10;
        margin: 0;
        i {
          font-size: 12px;
        }
      }
      p {
        margin-top: 35px;
        color: #fff;
        font-size: 16px;
      }
    }
  }
}
.iconCenter {
  width: 45%;
  img {
    width: 100%;
  }

  .polygon {
    display: flex;
    height: 33.3%;
    .imgStyle01 {
      background: url('@/assets/images/duobianxing.png') no-repeat;
      background-size: 100% 100%;
      .text {
        color: #a585ac;
      }
    }
    .imgStyle02 {
      background: url('@/assets/images/duobianxing2.png') no-repeat;
      background-size: 100% 100%;
      .text {
        color: #77ad83;
      }
    }
    .imgStyle03 {
      background: url('@/assets/images/duobianxing5.png') no-repeat;
      background-size: 100% 100%;
      .text {
        color: #77aaad;
      }
    }
    .imgStyle04 {
      background: url('@/assets/images/duobianxing4.png') no-repeat;
      background-size: 100% 100%;
      .text {
        color: #9aad77;
      }
    }
    .imgStyle05 {
      background: url('@/assets/images/duobianxing3.png') no-repeat;
      background-size: 100% 100%;
      .text {
        color: #7781ad;
      }
    }
    li {
      width: 118px;
      height: 118px;
      img {
        width: 100%;
      }
      .text {
        font-size: 20px;
        line-height: 90px;
        font-weight: bold;
      }
    }
  }
}
.columnCenterBottom {
  width: 100%;
  height: 32.5%;
  display: flex;
  justify-content: space-between;
  margin-top: 1.2%;
  margin-right: 10px;
}
.centerItem {
  width: 49.4%;
  height: 100%;
  position: relative;
  background: url('@/assets/images/box.png') no-repeat;
  background-size: 100% 100%;
}
.contentBox {
  height: 73%;
  width: 94%;
  margin: 0 auto;
}
.boxTitle {
  width: 100%;
  margin: 0 auto;
  position: relative;
  h2 {
    color: #e9e9e9;
    font-size: 16px;
    margin: 0;
    display: flex;
    line-height: 36px;
    position: absolute;
    left: 10px;
    i {
      display: inline-block;
      width: 36px;
      height: 36px;
      background: url('@/assets/images/title_ico.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  img {
    height: 47px;
  }
}
.boxTitlex {
  width: 100%;
  margin: 0 auto 10px auto;
  position: relative;
  .Title {
    position: absolute;
    left: 10px;
    width: 96%;
    display: flex;
    justify-content: space-between;
  }
  .tablist {
    display: flex;
    justify-content: flex-start;
    margin-right: 10px;
    margin-top: 10px;
    li {
      background-color: #74d5ff42;
      padding: 0 10px;
      height: 22px;
      margin-right: 5px;
      color: #74d5ff;
      border-radius: 1px;
      border: 1px solid #74d5ff77;
      cursor: pointer;
    }
    .active {
      background-color: #e9cc1050;
      color: #e9cc10;
      border: 1px solid #e9cc1086;
    }
  }
  h2 {
    color: #e9e9e9;
    font-weight: bold;
    font-size: 16px;
    margin: 0;
    display: flex;
    line-height: 36px;
    i {
      display: inline-block;
      width: 36px;
      height: 36px;
      background: url('@/assets/images/title_ico.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  img {
    height: 47px;
  }
}

.floatingBtn {
  position: fixed;
  right: 0;
  bottom: 300px;
  width: 100px;
  height: 100px;
  z-index: 9999999999;
  text-align: center;
  img {
    width: 100%;
  }
  h2 {
    font-size: 18px;
    color: #77b2f5;
    margin: 0;
  }
}

//six
.sixLeft {
  width: 24%;
  height: 82%;
  position: absolute;
  z-index: 1;
  top: 13%;
  left: 2%;
}
.sixRight {
  width: 24%;
  height: 82%;
  position: absolute;
  z-index: 1;
  top: 13%;
  right: 2%;
}
.sixTop {
  width: 46%;
  height: 13%;
  position: absolute;
  left: 27%;
  top: 14%;
  z-index: 9;
  pointer-events: none;
}
.sixBottom {
  width: 46%;
  height: 13%;
  position: absolute;
  left: 27%;
  bottom: 6%;
  z-index: 9;
    pointer-events: none;
}
//动效
.square {
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

//动效
//table
/* 自定义表格主题变量 */
.custom-el-table {
  --primary-color: #132449;
  --primary-light-color: #e8f3ff;
  --primary-dark-color: #0e42d2;
  --text-color: #333333;
  --text-light-color: #666666;
  --border-color: #e5e6eb;
  --hover-bg-color: #f5f7fa;
  --header-bg-color: rgba(255, 0, 0, 0.13);
  --header-text-color: #ffffff;
  --el-table-bg-color: transparent !important;
  --el-table-tr-bg-color: transparent !important;
}
.customSpan {
  cursor: pointer;
  color: #74d5ff;
  font-size: 12px;
}
.customSpanX {
  cursor: pointer;
  color: #084a66;
  font-size: 12px;
}
/* 表头样式 */
.custom-el-table .el-table__header th {
  background-color: #74d5ff3b !important;
  color: #74d5ff;
  border-top: 1px solid transparent !important;
  border-bottom: 1px solid transparent !important;
  position: relative;
  z-index: 1;
  padding: 4px 0;
  height: 30px !important;
}
.custom-el-table .el-table__inner-wrapper::before {
  background-color: transparent !important;
}
.custom-el-table .el-table__header th::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}
.custom-el-table .el-table__header th:hover::after {
  opacity: 1;
}

/* 表格内容样式 */
.custom-el-table .el-table__body td {
  padding: 0;
  color: #74d5ff;
  border-bottom: 1px solid #74d5ff3d;
}

/* 表格行悬停效果 */
.custom-el-table .el-table__body tr:hover > td {
  background-color: rgb(8, 85, 129) !important;
}

/* 表格偶数行样式 */
.custom-el-table .el-table__body tr:nth-child(even) {
  background-color: #74d5ff15;
}

/* 表格奇数行样式 */
.custom-el-table .el-table__body tr:nth-child(odd) {
  background-color: #74d5ff15;
}

/* 序号徽章样式 */
.index-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.custom-el-table .el-table__body tr:hover .index-badge {
  background-color: var(--primary-color);
  color: #74d5ff;
  transform: scale(1.1);
}

/* 表格滚动条样式 */
.custom-el-table .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-el-table .el-table__body-wrapper::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 4px;
}

.custom-el-table .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.custom-el-table .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* 表格空状态样式 */
.custom-el-table .el-table__empty-block {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.custom-el-table .el-table__empty-text {
  color: #999999;
  font-size: 14px;
  margin-top: 16px;
}

/* 加载状态遮罩 */
.table-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.table-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

.sbzxBox {
  display: flex;
  justify-content: space-around;
  .sbzxLeft {
    width: 50%;
    text-align: center;
    p {
      font-size: 18px;
      color: #74d5ff;
    }
  }
  .sbzxRight {
    width: 100%;
    display: flex;
    justify-content: space-between;
    text-align: center;
    li {
      width: 48%;
      p {
        color: #74d5ff;
      }
      span {
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        display: block;
        background: url('@/assets/images/u303.png');
        background-size: 100% 100%;
        font-size: 18px;
      }
    }
  }
  .sbzxCenter {
    margin-top: 10%;
    width: 100%;
    margin-bottom: 16%;
    text-align: center;
    p {
      color: #74d5ff;
    }
    span {
      width: 150px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      display: block;
      font-size: 18px;
      background: url('@/assets/images/u303.png');
      background-size: 100% 100%;
    }
  }
}
.byrenwu {
  width: 100%;
  height: 73%;
  background: url('@/assets/images/u338.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: space-around;
  padding: 2% 20px;
  .byrenwLeft {
    align-items: center;
    text-align: center;
    h2 {
      margin: 0;
      font-size: 24px;
      color: #74d5ff;
      margin-top: 10px;
    }
    h3 {
      font-size: 26px;
      color: #e9cc10;
      line-height: 60px;
    }
    p {
      font-size: 24px;
      color: #74d5ff;
    }
  }
  .byrenwRight {
    margin-top: 5px;
    li {
      color: #74d5ff;
      font-size: 14px;
      span {
        color: #e9cc10;
        margin-left: 5px;
      }
    }
  }
}

//第二屏
.scrennSecondStyle {
  width: 100%;
  height: 100%;

  .scrennSecondData {
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: space-between;
    padding-top: 2%;
    height: 12%;
    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 16%;
      span {
        color: #fff;
      }
      .icoBox {
        width: 60px;
        img {
          display: inline-block;
        }
      }
      .jirongText {
        width: 65%;
        display: flex;
        justify-content: flex-start;
        align-items: baseline;
      }
      p {
        font-size: 24px;
        font-weight: bold;
        color: #74d5ff;
      }
      h2 {
        font-size: 14px;
        margin: 0;
        color: #74d5ff;
      }
    }
  }
  .scrennSecondEchart {
    width: 100%;
    height: 82%;
    background: url('@/assets/images/box.png') no-repeat;
    background-size: 100% 100%;
    margin-top: 3%;
    padding: 2%;
  }
}

//第三屏
.scrennThreeStyle {
  width: 100%;
  height: 100%;
  position: relative;
}
.scrennThreeData {
  width: 100%;
  height: 30%;
  display: flex;
  justify-content: space-around;
  li {
    width: 25%;
    height: 76px;
    .selection {
      display: flex;
      justify-content: space-between;
    }
    h2 {
      color: #fff;
      font-size: 16px;
      margin: 0;
      line-height: 40px;
    }
    h3 {
      color: #fff;
      font-size: 24px;
      i {
        font-style: normal;
        font-size: 14px;
      }
    }
  }
}
.threeImageLeft {
  margin-left: 10px;
  img {
    margin-left: -10px;
  }
}
.threeImageRight {
  img {
    margin-right: 10px;
  }
}
.scrennThreeEchart {
  width: 80%;
  text-align: center;
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 16%;
  left: 10%;
  li {
    width: 137px;
    height: 137px;
    text-align: center;
    img {
      display: inline-block;
    }
    p {
      font-size: 20px;
      color: #e9cc10;
      font-weight: bold;
      i {
        font-size: 14px;
        color: #fff;
      }
    }
    h2 {
      font-size: 18px;
      margin: 0;
      color: #fff;
      font-weight: normal;
    }
  }
}

//第四屏
.scrennFourStyle {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  //第四屏
  .scrennFourData {
    width: 100%;
    display: flex;
    justify-content: space-between;
    // height: 11%;
    // margin-bottom: 2%;
    li {
      // width: 22%;
      // height: 76px;
      // background: url('@/assets/images/u1343.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-around;
      padding: 0 20px;
      h2 {
        color: #74d5ff;
        font-size: 30px;
        line-height: 76px;
        margin: 0;
      }
      h3 {
        color: #74d5ff;
        font-size: 14px;
        margin-top: 20px;
        font-weight: normal;
        i {
          font-style: normal;
          font-size: 18px;
          color: #fff;
          display: block;
        }
      }
    }
  }
  .scrennFourEchart {
    width: 100%;
    height: 82%;
    background: url('@/assets/images/box.png') no-repeat;
    background-size: 100% 100%;
    margin-top: 2%;
    padding: 2%;
  }
}

//弹框
/* 注意：去掉 style 的 scoped 属性，否则无法穿透 Element Plus 内置样式 */
/* 移除scoped，确保样式能穿透到弹框内部 */
.blue-dialog {
  /* 弹框整体容器背景（包括标题栏和内容区） */
  background-color: #073553;
  border: 1px solid #4f87c157;
  padding: 0 !important;
  .el-dialog {
    border: 1px solid #044b76; /* 蓝色边框 */
    border-radius: 8px; /* 圆角优化 */
  }

  /* 标题栏样式 */
  .el-dialog__header {
    color: #74d5ff;
    background: url('@/assets/images/tankuang.png') no-repeat;
    border-bottom: 1px solid #044b76; /* 分隔线 */
    padding: 8px 0;
  }

  /* 标题文本 */
  .el-dialog__title {
    color: #88d7ff;
    font-size: 16px;
    font-weight: bold;
    padding-left: 15px;
  }

  /* 关闭按钮 */
  .el-dialog__headerbtn .el-icon-close {
    color: #b7e7ff; /* 蓝色关闭图标 */
    &:hover {
      color: #084d9e;
      background-color: rgba(10, 101, 204, 0.1);
    }
  }

  /* 内容区样式 */
  .el-dialog__body {
    color: #96c9ff; /* 蓝色文本 */
    padding: 15px;
  }

  /* 底部按钮区 */
  .dialog-footer {
    padding: 8px 15px;
    border-top: 1px solid #044b76;
    text-align: right; /* 按钮右对齐 */
  }
  .dialog-footer .sureBtn {
    background-color: #00e67633;
    border-color: #00e676;
    color: #00e676;
    &:hover {
      background-color: #00e67756;
    }
  }
  .dialog-footer .resetBtn {
    background-color: #ff6b6b4d;
    border-color: #ff6b6b;
    color: #ff6b6b;
    &:hover {
      background-color: #ff6b6b75;
    }
  }

  /* 遮罩层（可选） */
  .v-modal {
    background-color: rgba(10, 101, 204, 0.15); /* 蓝色半透明遮罩 */
  }

  .el-dialog__footer {
    padding-top: 0;
  }
  .el-dialog__headerbtn {
    top: 10px;
    right: 15px;
    background: #00ceff3b;
    height: 20px;
    width: 20px;
    line-height: 20px;
    border: 1px solid #00e5ff7d;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #00aaff;
  }
}

.visibleBox {
  li {
    border: 1px solid #0093ff1f;
    margin-bottom: 10px;
    h2 {
      margin: 0;
      padding: 10px 0;
      background-color: #007bff1f;
      font-size: 14px;
      font-weight: normal;
      padding-left: 10px;
      border-bottom: 1px solid #0093ff1f;
    }
    h3 {
      margin: 0;
      padding: 10px 0;
      font-size: 14px;
      font-weight: normal;
      padding-left: 10px;
    }
  }
}
//地图版权
.BMap_cpyCtrl {
  display: none;
}
.anchorBL {
  display: none;
}
.big-data-select-container {
  margin-top: 8px;
}
