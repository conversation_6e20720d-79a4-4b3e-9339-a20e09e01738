<template>
  <div>
    <!-- 触发按钮 -->
    <button v-if="showTriggerButton" @click="togglePanel" class="chat-panel-trigger">
      <slot name="trigger">
        <span>AI Chat</span>
      </slot>
    </button>

    <!-- 遮罩层 -->
    <div v-show="isOpen" @click="closePanel" class="chat-panel-overlay" :class="{ open: isOpen }"></div>

    <!-- 主面板 -->
    <div class="chat-panel" :class="{ open: isOpen }" @click.stop>
      <div class="panel-header">
        <h3>{{ title }}</h3>
        <button class="close-btn" @click="closePanel">&times;</button>
      </div>

      <div class="panel-body">
        <div class="status-bar">
          <span class="status-indicator" :class="connectionStatusClass"></span>
          {{ connectionStatusText }}
          <button
              v-show="isThinking"
              class="stop-thinking-btn"
              @click="stopThinking"
          >
            停止
          </button>
        </div>

        <div class="panel-content" ref="messageArea">
          <div v-for="(message, index) in messages" :key="index" class="message"
               :class="[message.type, message.direction]">
            <div class="message-content">{{ message.content }}</div>
            <!--暂时移除时间戳 <div class="message-time">{{ message.time }} </div>-->
          </div>
        </div>

        <!-- 输入区域 (可选) -->
        <div v-if="showInput" class="input-area">
          <input type="text" v-model="userInput" @keyup.enter="sendMessage" placeholder="输入消息..."/>
          <button @click="sendMessage">发送</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, onMounted, onBeforeUnmount} from 'vue';

// Props
const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      buttonId: 'chat-panel-trigger',
      panelWidth: '500px',
      animationDuration: '0.3s',
      closeOnOutsideClick: true,
      ip: 'ai.tqlhsoft.com',
      wsPort: 8282,
      httpPort: 8383,
      maxReconnectAttempts: 5,
      reconnectDelay: 3000,
      pingInterval: 25000,
      pongTimeout: 10000,
      showInput: false,
      showTriggerButton: true,
      title: 'AI 消息'
    })
  }
});

// Refs
const isOpen = ref(false);
const socket = ref(null);
const reconnectCount = ref(0);
const messageMap = ref(new Map());
const audioContext = ref(null);
const audioQueue = ref([]);
const isAudioPlaying = ref(false);
const wavQueue = ref([]);
const isPlayingWav = ref(false);
const pingInterval = ref(null);
const pongTimeout = ref(null);
const lastPongTime = ref(null);
const connectionStatus = ref('connecting');
const messages = ref([]);
const userInput = ref('');
const messageArea = ref(null);
const sessionId = ref(null);
const isThinking = ref(true);

// Computed
const websocketUrl = computed(() => `ws://${props.config.ip}:${props.config.wsPort}/ws`);
const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接到服务器';
    case 'disconnected':
      return '连接已断开，正在尝试重新连接...';
    default:
      return '正在连接服务器...';
  }
});
const connectionStatusClass = computed(() => connectionStatus.value);
const showInput = computed(() => props.config.showInput);
const showTriggerButton = computed(() => props.config.showTriggerButton);
const title = computed(() => props.config.title);

// Methods
const togglePanel = () => (isOpen.value ? closePanel() : openPanel());

const openPanel = () => {
  isOpen.value = true;
  if (showInput.value) {
    const input = document.querySelector('.input-area input');
    if (input) input.focus();
  }
};

const closePanel = () => {
  isOpen.value = false;
};

const handleClickOutside = (event) => {
  if (isOpen.value && !event.target.closest('.chat-panel') && !event.target.closest('.chat-panel-trigger')) {
    closePanel();
  }
};

// WebSocket 方法
const connectWebSocket = () => {
  try {
    cleanupTimers();
    socket.value = new WebSocket(websocketUrl.value);
    connectionStatus.value = 'connecting';

    socket.value.onopen = () => {
      reconnectCount.value = 0;
      connectionStatus.value = 'connected';
      lastPongTime.value = Date.now();
      setupPing();
      addSystemMessage('已连接到服务器', 'success');
    };

    socket.value.onmessage = (event) => {
      const message = event.data;
      if (message === 'PONG') {
        handlePong();
        return;
      }
      if (message === 'PING') return;
      handleMessage(message);
    };

    socket.value.onclose = () => {
      cleanupTimers();
      connectionStatus.value = 'disconnected';
      reconnect();
    };

    socket.value.onerror = (error) => {
      console.error('WebSocket错误:', error);
      cleanupTimers();
      connectionStatus.value = 'disconnected';
      addSystemMessage(`连接错误: ${error.message || '未知错误'}`, 'error');
      reconnect();
    };
  } catch (error) {
    console.error('连接WebSocket失败:', error);
    cleanupTimers();
    connectionStatus.value = 'disconnected';
    addSystemMessage('连接失败', 'error');
    reconnect();
  }
};

// 消息处理方法
const handleMessage = (message) => {
  try {
    const data = typeof message === 'string' ? JSON.parse(message) : message;
    const msgId = data.msgId;
    const cmd = data.cmd || 0;
    let msgContent = data.message || '';
    let type = 'info';
    let direction = 'received';

    sessionId.value = data.sessionId;
    // console.log(data);

    switch (cmd) {
      case 8:
        const audioUrl = `http://${props.config.ip}:${props.config.httpPort}/profile${msgContent}`;
        wavQueue.value.push(audioUrl);
        if (!isPlayingWav.value) playNextWav();
        break;
      case 7:
        handleAudioData(msgContent);
        break;
      case 12:
        isThinking.value = false;
        cleanupAudio();
        break;
      case 13:
        isThinking.value = false;
        break;
      default:
        if (messageMap.value.has(msgId)) {
          const existingMsg = messageMap.value.get(msgId);
          existingMsg.content += msgContent;

          const index = messages.value.findIndex((m) => m.id === msgId);
          if (index !== -1) {
            messages.value[index].content = existingMsg.content;
          }
        } else {
          if (cmd === 3) {
            type = 'warning';
            direction = 'sent';
          } else if (cmd === 5) {
            msgContent = '思考过程: ' + msgContent;
            type = 'info';
          } else if (cmd === 6) {
            type = 'success';
          }

          const newMessage = {
            id: msgId,
            content: msgContent,
            type,
            direction
          };

          messages.value.push(newMessage);
          messageMap.value.set(msgId, newMessage);
        }

        isThinking.value = true;
        scrollToBottom();
        break;
    }

  } catch (e) {
    console.error('解析消息失败:', e);
    addSystemMessage('错误: 无效的消息格式', 'error');
  }
};

const addSystemMessage = (message, type = 'info', timeout = 3000) => {
  const msg = {
    id: Date.now().toString(),
    content: message,
    type: 'system ' + type,
    direction: ''
  };

  messages.value.push(msg);
  scrollToBottom();

  if (type !== 'error' && timeout > 0) {
    setTimeout(() => {
      const index = messages.value.findIndex((m) => m.id === msg.id);
      if (index !== -1) {
        messages.value.splice(index, 1);
      }
    }, timeout);
  }
};

const scrollToBottom = () => {
  if (messageArea.value) {
    messageArea.value.scrollTop = messageArea.value.scrollHeight;
  }
};

// 音频处理方法
const initAudioContext = () => {
  if (!audioContext.value) {
    audioContext.value = new (window.AudioContext || window.webkitAudioContext)();
    document.addEventListener(
        'click',
        () => {
          if (audioContext.value.state === 'suspended') {
            audioContext.value.resume();
          }
        },
        {once: true}
    );
  }
};

const handleAudioData = (wavData) => {
  if (!audioContext.value) {
    initAudioContext();
  }

  try {
    const binaryString = atob(wavData);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const wavDataOffset = 44;
    const audioData = new Int16Array(bytes.buffer, wavDataOffset, (bytes.length - wavDataOffset) / 2);
    const audioBuffer = audioContext.value.createBuffer(1, audioData.length, 22050);
    const channelData = audioBuffer.getChannelData(0);

    for (let i = 0; i < audioData.length; i++) {
      channelData[i] = audioData[i] / 32768.0;
    }

    audioQueue.value.push(audioBuffer);
    if (!isAudioPlaying.value) {
      playNextAudio();
    }
  } catch (error) {
    console.error('处理音频数据时出错:', error);
  }
};

const playNextAudio = () => {
  if (audioQueue.value.length === 0) {
    isAudioPlaying.value = false;
    return;
  }

  isAudioPlaying.value = true;
  const audioBuffer = audioQueue.value.shift();
  const source = audioContext.value.createBufferSource();

  source.buffer = audioBuffer;
  source.connect(audioContext.value.destination);

  source.onended = () => {
    playNextAudio();
  };

  source.onerror = (error) => {
    console.error('播放音频时出错:', error);
    playNextAudio();
  };

  source.start(0);
};

const playNextWav = () => {
  if (wavQueue.value.length === 0) {
    isPlayingWav.value = false;
    return;
  }

  isPlayingWav.value = true;
  const audio = new Audio(wavQueue.value.shift());

  audio.onended = () => {
    playNextWav();
  };

  audio.onerror = (e) => {
    console.error('音频播放错误:', e);
    playNextWav();
  };

  audio.play().catch((e) => {
    console.error('播放失败:', e);
    playNextWav();
  });
};

// WebSocket 连接维护方法
const setupPing = () => {
  cleanupTimers();

  const sendPing = () => {
    if (socket.value?.readyState === WebSocket.OPEN) {
      try {
        socket.value.send('PING');
        setupPongTimeout();
      } catch (error) {
        console.error('发送PING失败:', error);
      }
    }
  };

  pingInterval.value = setInterval(sendPing, props.config.pingInterval);
  sendPing();
};

const setupPongTimeout = () => {
  if (pongTimeout.value) {
    clearTimeout(pongTimeout.value);
  }

  pongTimeout.value = setTimeout(() => {
    const timeSinceLastPong = Date.now() - lastPongTime.value;
    if (timeSinceLastPong > props.config.pingInterval + props.config.pongTimeout) {
      console.warn('服务器响应超时，正在重新连接...');
      connectionStatus.value = 'disconnected';
      addSystemMessage('服务器无响应，正在重新连接...', 'warning');
      socket.value?.close();
      reconnect();
    }
  }, props.config.pongTimeout);
};

const handlePong = () => {
  lastPongTime.value = Date.now();
  if (pongTimeout.value) {
    clearTimeout(pongTimeout.value);
    pongTimeout.value = null;
  }
};

const reconnect = () => {
  cleanupTimers();
  if (reconnectCount.value < props.config.maxReconnectAttempts) {
    reconnectCount.value++;
    const delay = props.config.reconnectDelay * reconnectCount.value;
    addSystemMessage(`正在重连... (尝试 ${reconnectCount.value}/${props.config.maxReconnectAttempts})`, 'warning');

    setTimeout(() => {
      connectWebSocket();
    }, delay);
  } else {
    addSystemMessage('重连次数已达上限，请刷新页面重试', 'error');
  }
};

// 清理方法
const cleanupTimers = () => {
  if (pingInterval.value) {
    clearInterval(pingInterval.value);
    pingInterval.value = null;
  }
  if (pongTimeout.value) {
    clearTimeout(pongTimeout.value);
    pongTimeout.value = null;
  }
};

const cleanupAudio = () => {
  if (audioContext.value) {
    audioContext.value.close();
    audioContext.value = null;
  }
  audioQueue.value = [];
  wavQueue.value = [];
  isAudioPlaying.value = false;
  isPlayingWav.value = false;
};

const cleanup = () => {
  if (socket.value) {
    socket.value.close();
    socket.value = null;
  }
  cleanupTimers();
  cleanupAudio();
};

// 发送消息方法
const sendMessage = () => {
  if (userInput.value.trim() && socket.value?.readyState === WebSocket.OPEN) {
    socket.value.send(userInput.value);
    addMessage(
        {
          msgId: Date.now().toString(),
          message: userInput.value,
          cmd: 3
        },
        true
    );
    userInput.value = '';
  }
};

// 公开API方法
const addMessage = (message, isSent = false) => {
  handleMessage(
      JSON.stringify({
        ...message,
        direction: isSent ? 'sent' : 'received'
      })
  );
};

// 添加默认欢迎消息
const addWelcomeMessage = () => {
  messages.value.push({
    msgId: Date.now(),
    type: 'received',
    content: '您好，我是参管家',
    cmd: 6,
    time: new Date().toLocaleTimeString()
  });
  scrollToBottom();
};

// 停止思考方法
const stopThinking = () => {
  if (!sessionId.value) {
    return;
  }

  const stopCommand = JSON.stringify({
    cmd: 11,
    sessionId: sessionId.value
  });

  if (socket.value && socket.value.readyState === WebSocket.OPEN) {
    socket.value.send(stopCommand);
  }

  // 延时确保按钮一定会隐藏
  setTimeout(() => {
    sessionId.value = null;
    isThinking.value = false;
  }, 500);
};

// 暴露组件方法
defineExpose({
  openPanel: openPanel,
  closePanel: closePanel,
  togglePanel: togglePanel,
  addMessage: addMessage
});

// 生命周期
onMounted(() => {
  addWelcomeMessage();
  connectWebSocket();
  if (props.config.closeOnOutsideClick) {
    document.addEventListener('click', handleClickOutside);
  }
});

onBeforeUnmount(() => {
  cleanup();
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.chat-panel-trigger {
  position: fixed;
  right: 20px;
  bottom: 20px;
  padding: 10px 15px;
  background: linear-gradient(90deg, rgba(43, 137, 243, 0.9) 0%, rgba(0, 122, 254, 0.9) 100%);
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 998;
  transition: all 0.3s ease;
}

.chat-panel-trigger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.chat-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.chat-panel-overlay.open {
  opacity: 1;
  pointer-events: auto;
}

.chat-panel {
  position: fixed;
  top: 0;
  right: -500px;
  width: 500px;
  height: 100%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  transition: right 0.3s ease-in-out;
}

.chat-panel.open {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: linear-gradient(90deg, rgba(43, 137, 243, 0.9) 0%, rgba(0, 122, 254, 0.9) 100%);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  color: white;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.panel-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 0;
}

.status-bar {
  padding: 10px 16px;
  background: rgba(0, 0, 0, 0.1);
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.connected {
  background: #4caf50;
  box-shadow: 0 0 8px #4caf50;
}

.status-indicator.connecting {
  background: #ffc107;
  box-shadow: 0 0 8px #ffc107;
  animation: pulse 1.5s infinite;
}

.status-indicator.disconnected {
  background: #f44336;
  box-shadow: 0 0 8px #f44336;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.stop-thinking-btn {
  margin-left: auto;
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 4px 12px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  letter-spacing: 0.5px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stop-thinking-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.stop-thinking-btn:active {
  transform: translateY(0);
}

.panel-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #f5f5f5;
}

.message {
  max-width: 80%;
  padding: 10px 14px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 4px;
}

.message.sent {
  background-color: #4caf50;
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 4px;
  text-align: right;
}

.message.received {
  background-color: white;
  color: #333;
  margin-right: auto;
  border-bottom-left-radius: 4px;
}

.message.system {
  margin: 8px auto;
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 12px;
  text-align: center;
  padding: 6px 12px;
  border-radius: 12px;
  max-width: 90%;
  transition: opacity 0.5s ease;
}

.message.system.error {
  background-color: #ffebee;
  color: #d32f2f;
}

.message.system.warning {
  background-color: #fff8e1;
  color: #ff8f00;
}

.message.system.success {
  background-color: #e8f5e9;
  color: #388e3c;
}

.message-time {
  font-size: 11px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
  text-align: right;
}

.message.sent .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.message-content {
  word-break: break-word;
}

.input-area {
  padding: 12px;
  border-top: 1px solid #e0e0e0;
  background: #f9f9f9;
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-area input {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
  transition: border-color 0.3s;
}

.input-area input:focus {
  border-color: #4caf50;
}

.input-area button {
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.input-area button:hover {
  background: #43a047;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .chat-panel {
    width: 100%;
    right: -100%;
  }
}
</style>
