<template>
  <div class="headerBox">
    <div class="headerTitle"><strong>吉参云大数据平台</strong></div>
    <ul class="headerBtn">
      <li 
        class="flbtn" 
        :class="{ active: isActive('/scrennFirst') }" 
        @click="navigate('/scrennFirst')"
      >总览</li>
      <li 
        class="flbtn" 
        :class="{ active: isActive('/scrennSecond') }" 
        @click="navigate('/scrennSecond')"
      >科技</li>
      <li 
        class="flbtn" 
        :class="{ active: isActive('/scrennThree') }" 
        @click="navigate('/scrennThree')"
      >金融</li>
      <li 
        class="frbtn" 
        :class="{ active: isActive('/scrennFive') }" 
        @click="navigate('/scrennFive')"
      >三产</li>
      <li 
        class="frbtn" 
        :class="{ active: isActive('/scrennSix') }" 
        @click="navigate('/scrennSix')"
      >二产</li>
      <li 
        class="frbtn" 
        :class="{ active: isActive('/scrennFour') }" 
        @click="navigate('/scrennFour')"
      >一产</li>
    </ul>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// 判断当前路由是否匹配
const isActive = (path) => {
  // 使用正则表达式匹配路径前缀
  const regex = new RegExp(`^${path}(/|$)`);
  return regex.test(route.path);
};

const navigate = (path) => {
  router.push(path);
};
</script>

<style scoped lang="scss">
.headerBox {
  width: 100%;
  padding: 40px 65px 0 65px;
  position: absolute;
  z-index: 9;
  
  .headerTitle {
    width: 100%;
    position: absolute;
    left: 0;
    top: 10px;
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    background-image: linear-gradient(to top, #ffffff, #74D5FF);
    -webkit-background-clip: text;
    color: transparent;
    letter-spacing: 6px;
    pointer-events: none;
     font-family: Heiti TC, serif;
     
  }
  
  .headerBtn {
    clear: both;
    overflow: hidden;
    
    li {
      width: 120px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: #ffffff;
      font-weight: bold;
      font-size: 16px;
      cursor: pointer;
    }
    
    .flbtn {
      background: url("@/assets/images/nav_left.png") no-repeat;
      background-size: 100% 100%;
      float: left;
      margin-right: 10px;
      color: #74D5FF;
    }
    
    .frbtn {
      background: url("@/assets/images/nav_right.png") no-repeat;
      background-size: 100% 100%;
      float: right;
      margin-left: 10px;
      color: #74D5FF;
    }
    
    .flbtn:hover,
    .frbtn:hover,
    .active {
      color: #e9cc10 !important;
    }
    
    .flbtn.active {
      background: url("@/assets/images/nav_left_h.png") no-repeat !important;
      background-size: 100% 100% !important;
    }
    
    .frbtn.active {
      background: url("@/assets/images/nav_right_h.png") no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
}
</style>  