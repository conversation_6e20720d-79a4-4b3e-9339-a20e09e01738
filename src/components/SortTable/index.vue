<template>
  <div class="scrollable-table-container" ref="tableContainer">
    <!-- 固定部分表格 - 只显示表头和前3行数据 -->
    <el-table
      ref="fixedTable"
      :data="fixedData"
      :border="border"
      :style="{ width: '100%', tableLayout: 'fixed' }"
      class="custom-el-table fixed-table"
      show-overflow-tooltip
     @row-click="emitCustomEvent" 
    >
      <slot></slot>
    </el-table>
    
    <!-- 滚动部分表格 - 隐藏表头，只显示数据行 -->
    <div class="scroll-container" ref="scrollContainer">
      <el-table
        ref="scroll_table"
        :data="scrollData"
        :border="border"
        :style="{ width: '100%', tableLayout: 'fixed' }"
        :empty-text="emptyText"
        @mouseenter="autoScroll(true)"
        @mouseleave="autoScroll(false)"
        class="custom-el-table scroll-table"
        show-overflow-tooltip
        @row-click="emitCustomEvent" 
      >
        <!-- 隐藏表头 -->
        <template #header>
          <div style="height: 0; overflow: hidden;"></div>
        </template>
        <slot></slot>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick,defineEmits } from "vue";

// 父组件可配置参数
const props = defineProps({
  // 数据源（必须传入）
  data: {
    type: Array,
    default: () => [],
    required: true
  },
  // 是否显示边框
  border: {
    type: Boolean,
    default: false
  },
  // 无数据提示文本
  emptyText: {
    type: String,
    default: "暂无数据"
  },
  // 滚动间隔（ms），数值越大越慢
  scrollDelay: {
    type: Number,
    default: 30
  },
  // 最小滚动数据量（少于此数量不滚动）
  minScrollCount: {
    type: Number,
    default: 4
  }
});

// 固定数据和滚动数据
const fixedData = ref([]);
const scrollData = ref([]);
// 容器DOM引用
const tableContainer = ref(null);
const scrollContainer = ref(null);
// 表格实例引用
const fixedTable = ref(null);
const scroll_table = ref(null);

// 分割数据
const splitData = () => {
  if (props.data.length <= props.minScrollCount) {
    fixedData.value = [...props.data];
    scrollData.value = [];
    return;
  }
  
  // 前三条数据固定
  fixedData.value = props.data.slice(0, 3);
  // 第四条开始的数据滚动
  scrollData.value = [...props.data.slice(3), ...props.data.slice(3)]; // 复制一份实现无缝滚动
};

// 监听父组件数据变化
watch(
  () => props.data,
  (newVal) => {
    splitData();
    // 数据变化后重启滚动
    autoScroll(true);
    // 等待表格重新渲染后同步列宽
    nextTick(syncColumnWidths);
    setTimeout(() => autoScroll(false), 500);
  },
  { deep: true }
);

// 初始化数据
splitData();

// 滚动相关变量
let ST = null;
const scroll_timer = ref(null);
// 监听父组件高度变化的ResizeObserver
let resizeObserver = null;

// 滚动核心函数
const scrollTable = () => {
  const scrollWrapper = scrollContainer.value;
  if (!scrollWrapper) return;
  
  scrollWrapper.scrollTop += 1;

  // 滚动到末尾时重置位置
  if (scrollWrapper.scrollTop >= scrollWrapper.scrollHeight / 2) {
    scrollWrapper.scrollTop = 0;
  }

  // 控制滚动频率
  ST = setTimeout(() => {
    scroll_timer.value = requestAnimationFrame(scrollTable);
  }, props.scrollDelay);
};

// 滚动控制
const autoScroll = (stop) => {
  const scrollWrapper = scrollContainer.value;

  // 停止滚动
  if (stop) {
    if (ST) { clearTimeout(ST); ST = null; }
    if (scroll_timer.value) { cancelAnimationFrame(scroll_timer.value); scroll_timer.value = null; }
    return;
  }

  // 数据不足时不滚动
  if (props.data.length <= props.minScrollCount || !scrollData.value.length) return;

  // 清除残留定时器
  if (ST) { clearTimeout(ST); ST = null; }
  if (scroll_timer.value) { cancelAnimationFrame(scroll_timer.value); scroll_timer.value = null; }

  // 启动滚动
  ST = setTimeout(() => {
    scroll_timer.value = requestAnimationFrame(scrollTable);
  }, props.scrollDelay);
};

// 同步两个表格的列宽
const syncColumnWidths = () => {
  nextTick(() => {
    const fixedColumns = fixedTable.value?.$el.querySelectorAll('.el-table__header th');
    const scrollColumns = scroll_table.value?.$el.querySelectorAll('.el-table__body td');
    
    if (!fixedColumns || !scrollColumns || fixedColumns.length !== scrollColumns.length) return;
    
    fixedColumns.forEach((fixedCol, index) => {
      if (scrollColumns[index]) {
        const width = fixedCol.offsetWidth;
        scrollColumns[index].style.width = `${width}px`;
        scrollColumns[index].style.minWidth = `${width}px`;
      }
    });
  });
};

// 监听父组件高度变化
const watchContainerResize = () => {
  if (!tableContainer.value) return;

  resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      // 调整滚动区域高度
      adjustScrollContainerHeight();
      // 重启滚动
      autoScroll(true);
      nextTick(() => {
        syncColumnWidths();
        autoScroll(false);
      });
    }
  });

  resizeObserver.observe(tableContainer.value);
};

// 调整滚动容器高度
const adjustScrollContainerHeight = () => {
  if (!tableContainer.value || !scrollContainer.value) return;
  
  // 获取固定表格高度
  const fixedTableEl = tableContainer.value.querySelector('.fixed-table');
  const fixedHeight = fixedTableEl ? fixedTableEl.offsetHeight : 0;
  
  // 设置滚动容器高度
  scrollContainer.value.style.height = `calc(100% - ${fixedHeight}px)`;
};

const emits = defineEmits(['customEvent']);  // 声明要派发的事件
const emitCustomEvent = (...args) => {
  // 将表格行点击的参数（row, column, event）透传给父组件
  emits('customEvent', ...args);
};
// 组件挂载后初始化
onMounted(() => {
  // 调整滚动容器高度
  adjustScrollContainerHeight();
  
  // 同步列宽
  syncColumnWidths();
  
  // 监听高度变化
  watchContainerResize();
  
  // 延迟启动滚动
  setTimeout(() => autoScroll(false), 1000);
});

// 组件卸载时清理
onUnmounted(() => {
  // 停止滚动
  autoScroll(true);
  if (scroll_timer.value) {
    cancelAnimationFrame(scroll_timer.value);
    scroll_timer.value = null;
  }
  
  // 停止监听高度变化
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});

// 暴露方法
defineExpose({
  scroll_table,
  autoScroll
});
</script>

<style scoped>
.scrollable-table-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-table {
  position: relative;
  z-index: 1;
}

.scroll-container {
  overflow: hidden;
  width: 100%;
}

.scroll-table {
  /* 确保表格列宽一致 */
  position: relative;
}

/* 确保表格列宽一致的技巧 */
::v-deep .fixed-table th,
::v-deep .scroll-table td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 隐藏滚动表格的表头 */
::v-deep .scroll-table .el-table__header-wrapper {
  height: 0 !important;
  overflow: hidden !important;
}
</style>