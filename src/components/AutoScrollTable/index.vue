<template>
  <!-- 外层容器：通过position实现高度自适应父组件 -->
  <div class="table-container" ref="tableContainer">
    <el-table
      ref="scroll_table"
      :data="processedData"
      :border="border"
      :style="{ width: '100%', height: '100%' }" 
      :empty-text="emptyText"
      @mouseenter="autoScroll(true)"
      @mouseleave="autoScroll(false)"
      @row-click="emitCustomEvent" 
      class="custom-el-table"
      show-overflow-tooltip
    >
      <!-- 插槽：父组件自定义表格列内容 -->
      <slot></slot>
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick,defineEmits } from "vue";

// 父组件可配置参数
const props = defineProps({
  // 数据源（必须传入）
  data: {
    type: Array,
    default: () => [],
    required: true
  },
  // 是否显示边框
  border: {
    type: Boolean,
    default: false
  },
  // 无数据提示文本
  emptyText: {
    type: String,
    default: "暂无数据"
  },
  // 滚动间隔（ms），数值越大越慢
  scrollDelay: {
    type: Number,
    default: 30
  },
  // 最小滚动数据量（少于此数量不滚动）
  minScrollCount: {
    type: Number,
    default: 4
  }
});

// 处理后的数据（复制原数据实现无缝滚动）
const processedData = ref([]);
// 容器DOM引用（用于监听高度变化）
const tableContainer = ref(null);
// 表格实例引用
const scroll_table = ref(null);

// 复制数据逻辑（实现无缝滚动）
const concatData = () => {
  if (props.data.length <= props.minScrollCount) {
    processedData.value = [...props.data];
    return;
  }
  // 复制原数据并追加，确保滚动衔接
  processedData.value = [...props.data, ...props.data];
};

// 监听父组件数据变化，重新处理数据
watch(
  () => props.data,
  (newVal) => {
    concatData();
    // 数据变化后重启滚动
    autoScroll(true);
    setTimeout(() => autoScroll(false), 500);
  },
  { deep: true }
);

// 初始化数据
concatData();

// 滚动相关变量
let ST = null;
const scroll_timer = ref(null);
// 监听父组件高度变化的ResizeObserver
let resizeObserver = null;

// 滚动核心函数
const scrollTable = (tableWrapper) => {
  if (!tableWrapper) return;
  tableWrapper.scrollTop += 1;

  // 滚动到末尾时重置位置（无缝滚动关键）
  if (tableWrapper.scrollTop + tableWrapper.clientHeight >= tableWrapper.scrollHeight) {
    // 重置到原数据长度位置（精准衔接）
    const resetPosition = tableWrapper.scrollTop - (props.data.length * (tableWrapper.scrollHeight / processedData.value.length));
    tableWrapper.scrollTop = Math.max(0, resetPosition);
  }

  // 控制滚动频率
  ST = setTimeout(() => {
    scroll_timer.value = requestAnimationFrame(() => scrollTable(tableWrapper));
  }, props.scrollDelay);
};

// 滚动控制（stop为true时停止）
const autoScroll = (stop) => {
  const divData = scroll_table.value?.$refs.bodyWrapper;
  const tableWrapper = divData?.firstElementChild?.firstElementChild;

  // 停止滚动
  if (stop) {
    if (ST) { clearTimeout(ST); ST = null; }
    if (scroll_timer.value) { cancelAnimationFrame(scroll_timer.value); scroll_timer.value = null; }
    return;
  }

  // 数据不足时不滚动
  if (props.data.length <= props.minScrollCount) return;
  if (tableWrapper && (tableWrapper.scrollHeight / 2 < tableWrapper.clientHeight)) return;

  // 清除残留定时器
  if (ST) { clearTimeout(ST); ST = null; }
  if (scroll_timer.value) { cancelAnimationFrame(scroll_timer.value); scroll_timer.value = null; }

  // 启动滚动
  ST = setTimeout(() => {
    scroll_timer.value = requestAnimationFrame(() => scrollTable(tableWrapper));
  }, props.scrollDelay);
};

// 监听父组件高度变化，自动调整表格高度
const watchContainerResize = () => {
  if (!tableContainer.value) return;

  // 创建ResizeObserver监听容器高度变化
  resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      // 容器高度变化时，重新初始化滚动（避免高度异常）
      autoScroll(true);
      nextTick(() => {
        autoScroll(false);
      });
    }
  });

  resizeObserver.observe(tableContainer.value);
};

// 组件挂载后初始化
onMounted(() => {
  // 监听高度变化
  watchContainerResize();
  // 延迟启动滚动（确保表格渲染完成）
  setTimeout(() => autoScroll(false), 1000);
});


const emits = defineEmits(['customEvent']);  // 声明要派发的事件
const emitCustomEvent = (...args) => {
  // 将表格行点击的参数（row, column, event）透传给父组件
  emits('customEvent', ...args);
};

// 组件卸载时清理
onUnmounted(() => {
  // 停止滚动
  autoScroll(true);
  if (scroll_timer.value) {
    cancelAnimationFrame(scroll_timer.value);
    scroll_timer.value = null;
  }
  // 停止监听高度变化
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});

// 暴露表格实例（供父组件调用）
defineExpose({
  scroll_table,
  autoScroll
});
</script>

<style scoped>
/* 容器样式：自适应父组件高度 */
.table-container {
  width: 100%;
  height: 100%;  /* 关键：占满父组件高度 */
  position: relative;
  overflow: hidden;
}
</style>