using System.Collections;
using System.Collections.Generic;
using UnityEngine;

pIEnumerator <PERSON><PERSON><PERSON>(string fileName) {
    string url = System.IO.Path.Combine(Application.streamingAssetsPath, fileName);
    UnityWebRequest request = UnityWebRequest.Get(url);
    yield return request.SendWebRequest();
    
    if (request.result == UnityWebRequest.Result.Success) {
        videoPlayer.url = request.uri; // 关键：使用URI而非本地路径
        videoPlayer.Play();
    }
}

