{"name": "ruoyi", "version": "3.9.0", "description": "若依管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "13.3.0", "axios": "1.9.0", "clipboard": "2.0.11", "echarts": "5.6.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "element-plus": "2.9.9", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "moment": "^2.30.1", "nprogress": "0.2.0", "pinia": "3.0.2", "splitpanes": "4.0.4", "swiper": "^11.2.8", "vue": "3.5.16", "vue-baidu-map-3x": "^1.0.40", "vue-cropper": "1.1.1", "vue-router": "4.5.1", "vue3-video-play": "^1.3.1-beta.6", "vuedraggable": "4.1.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.1", "@vitejs/plugin-vue": "5.2.4", "less": "^4.3.0", "prettier": "3.6.2", "sass-embedded": "1.89.1", "unplugin-auto-import": "0.18.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}, "overrides": {"quill": "2.0.2"}}